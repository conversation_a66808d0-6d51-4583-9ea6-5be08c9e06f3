# ----------------------------------------------------------------------------
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml
#
# ----------------------------------------------------------------------------

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code using: baml-cli generate
# baml-cli is available with the baml package.

import typing
import typing_extensions
from enum import Enum


from pydantic import BaseModel, ConfigDict


import baml_py

CheckT = typing_extensions.TypeVar('CheckT')
CheckName = typing_extensions.TypeVar('CheckName', bound=str)

class Check(BaseModel):
    name: str
    expression: str
    status: str
class Checked(BaseModel, typing.Generic[CheckT, CheckName]):
    value: CheckT
    checks: typing.Dict[CheckName, Check]

def get_checks(checks: typing.Dict[CheckName, Check]) -> typing.List[Check]:
    return list(checks.values())

def all_succeeded(checks: typing.Dict[CheckName, Check]) -> bool:
    return all(check.status == "succeeded" for check in get_checks(checks))
# #########################################################################
# Generated enums (12)
# #########################################################################

class Agent(str, Enum):
    ROADMAP = "ROADMAP"
    CrossIndexing = "CrossIndexing"

class CodeStorageAction(str, Enum):
    Add = "Add"
    Remove = "Remove"

class CodeStorageAction_CrossIndexing(str, Enum):
    Add = "Add"
    Remove = "Remove"

class ContractRole(str, Enum):
    Provider = "Provider"
    Consumer = "Consumer"
    Both = "Both"

class FileOperation(str, Enum):
    Create = "Create"
    Modify = "Modify"
    Delete = "Delete"

class ImpactLevel(str, Enum):
    High = "High"
    Medium = "Medium"
    Low = "Low"
    NoImpact = "NoImpact"

class Status_CrossIndexing(str, Enum):
    Pending = "Pending"
    Current = "Current"
    Completed = "Completed"

class TaskOperationAction(str, Enum):
    Add = "Add"
    Remove = "Remove"
    Move = "Move"

class TaskOperationAction_CrossIndexing(str, Enum):
    Add = "Add"
    Remove = "Remove"
    Move = "Move"

class TaskStatus(str, Enum):
    PENDING = "PENDING"
    CURRENT = "CURRENT"
    COMPLETED = "COMPLETED"

class TechnologyType(str, Enum):
    HTTP_HTTPS = "HTTP_HTTPS"
    WebSockets = "WebSockets"
    GRPC = "GRPC"
    GraphQL = "GraphQL"
    MessageQueue = "MessageQueue"
    Unknown = "Unknown"

class ToolName(str, Enum):
    Database = "Database"
    SearchKeyword = "SearchKeyword"
    SearchKeywordWithoutProjectName = "SearchKeywordWithoutProjectName"
    SemanticSearch = "SemanticSearch"
    ListFiles = "ListFiles"
    ListFilesWithoutProjectName = "ListFilesWithoutProjectName"
    Completion = "Completion"

# #########################################################################
# Generated classes (49)
# #########################################################################

class AddTask(BaseModel):
    id: int
    description: str

class BaseCompletionParams(BaseModel):
    result: str

class BasePromptParams(BaseModel):
    system_info: "SystemInfoParams"
    project_context: "ProjectContext"

class ChangeInstruction(BaseModel):
    description: str
    current_state: typing.Optional[str] = None
    target_state: str
    start_line: typing.Optional[int] = None
    end_line: typing.Optional[int] = None
    additional_notes: typing.Optional[str] = None

class CodeConnection(BaseModel):
    id: str
    file: str
    start_line: int
    end_line: int
    description: str

class CodeManagerResponse(BaseModel):
    thinking: typing.Optional[str] = None
    connection_code: typing.Optional[typing.List["CodeConnection"]] = None

class CodeStorage(BaseModel):
    action: CodeStorageAction
    id: str
    file: str
    start_line: int
    end_line: int
    description: str

class CodeStorage_CrossIndexing(BaseModel):
    action: CodeStorageAction_CrossIndexing
    id: str
    file: str
    start_line: int
    end_line: int
    description: str

class CompletionResponse_CrossIndexing(BaseModel):
    result: str

class CompletionToolCall(BaseModel):
    tool_name: typing_extensions.Literal['attempt_completion']
    parameters: "BaseCompletionParams"

class CompletionToolCall_CrossIndexing(BaseModel):
    tool_name: typing_extensions.Literal['attempt_completion']
    parameters: "CompletionResponse_CrossIndexing"

class ConnectionDetail(BaseModel):
    snippet_lines: str
    description: str

class ConnectionMatch(BaseModel):
    incoming_id: str
    outgoing_id: str
    match_confidence: str
    match_reason: str

class ConnectionMatchingResponse(BaseModel):
    matches: typing.Optional[typing.List["ConnectionMatch"]] = None

class ConnectionSplittingResponse(BaseModel):
    incoming_connections: typing.Optional[typing.Dict[typing.Union[TechnologyType, str], typing.Dict[str, typing.List["ConnectionDetail"]]]] = None
    outgoing_connections: typing.Optional[typing.Dict[typing.Union[TechnologyType, str], typing.Dict[str, typing.List["ConnectionDetail"]]]] = None
    summary: typing.Optional[str] = None

class Contract(BaseModel):
    contract_id: str
    contract_type: str
    name: str
    description: str
    role: ContractRole
    interface: typing.Dict[str, str]
    input_format: typing.Optional[typing.List["ContractField"]] = None
    output_format: typing.Optional[typing.List["ContractField"]] = None
    error_codes: typing.Optional[typing.List[str]] = None
    authentication_required: typing.Optional[bool] = None
    examples: str
    instructions: typing.Optional[str] = None

class ContractField(BaseModel):
    name: str
    type: str
    required: bool
    description: typing.Optional[str] = None
    validation: typing.Optional[str] = None
    nested: typing.Optional[typing.List["ContractField"]] = None

class CrossIndexingResponse(BaseModel):
    thinking: typing.Optional[str] = None
    tool_call: typing.Optional[typing.Union["ListFilesToolCallWithoutProjectName", "DatabaseToolCall", "SearchKeywordToolCallWithoutProjectName", "CompletionToolCall_CrossIndexing"]] = None
    sutra_memory: "SutraMemoryParams_CrossIndexing"

class DatabaseParams(BaseModel):
    query_name: str
    file_path: typing.Optional[str] = None
    start_line: typing.Optional[int] = None
    end_line: typing.Optional[int] = None
    block_id: typing.Optional[str] = None
    fetch_next_chunk: typing.Optional[bool] = None

class DatabaseToolCall(BaseModel):
    tool_name: typing_extensions.Literal['database']
    parameters: "DatabaseParams"

class FileChange(BaseModel):
    file_path: str
    operation: FileOperation
    instructions: typing.List["ChangeInstruction"]

class FileChanges(BaseModel):
    modified: typing.Optional[typing.List[str]] = None
    added: typing.Optional[typing.List[str]] = None
    deleted: typing.Optional[typing.List[str]] = None

class ListFilesParams(BaseModel):
    path: typing.Optional[str] = None
    project_name: typing.Optional[str] = None
    recursive: typing.Optional[bool] = None
    fetch_next_chunk: typing.Optional[bool] = None

class ListFilesParamsWithoutProjectName(BaseModel):
    path: str
    recursive: typing.Optional[bool] = None
    fetch_next_chunk: typing.Optional[bool] = None

class ListFilesToolCall(BaseModel):
    tool_name: typing_extensions.Literal['list_files']
    parameters: "ListFilesParams"

class ListFilesToolCallWithoutProjectName(BaseModel):
    tool_name: typing_extensions.Literal['list_files']
    parameters: "ListFilesParamsWithoutProjectName"

class Project(BaseModel):
    name: str
    path: str
    description: str

class ProjectContext(BaseModel):
    projects: typing.List["Project"]

class ProjectRoadmap(BaseModel):
    project_name: str
    project_path: str
    impact_level: ImpactLevel
    reasoning: str
    implementation_plan: typing.List[str]
    changes: typing.Optional[typing.List["FileChange"]] = None
    contracts: typing.Optional[typing.List["Contract"]] = None

class RoadmapAgentParams(BaseModel):
    context: str
    prompt_params: "RoadmapPromptParams"

class RoadmapCompletionParams(BaseModel):
    projects: typing.List["ProjectRoadmap"]
    summary: str

class RoadmapCompletionToolCall(BaseModel):
    tool_name: typing_extensions.Literal['attempt_completion']
    parameters: typing.Union["RoadmapCompletionParams", "BaseCompletionParams"]

class RoadmapPromptParams(BaseModel):
    base_params: "BasePromptParams"

class RoadmapResponse(BaseModel):
    thinking: typing.Optional[str] = None
    tool_call: typing.Optional[typing.Union["DatabaseToolCall", "SearchKeywordToolCall", "SemanticSearchToolCall", "ListFilesToolCall", "RoadmapCompletionToolCall"]] = None
    sutra_memory: "SutraMemoryParams"

class SearchKeywordParams(BaseModel):
    keyword: str
    before_lines: typing.Optional[int] = None
    after_lines: typing.Optional[int] = None
    case_sensitive: typing.Optional[bool] = None
    regex: typing.Optional[bool] = None
    file_paths: typing.Optional[str] = None
    project_name: typing.Optional[str] = None
    fetch_next_chunk: typing.Optional[bool] = None

class SearchKeywordParamsWithoutProjectName(BaseModel):
    keyword: str
    file_paths: typing.Optional[str] = None
    before_lines: typing.Optional[int] = None
    after_lines: typing.Optional[int] = None
    case_sensitive: typing.Optional[bool] = None
    regex: typing.Optional[bool] = None
    fetch_next_chunk: typing.Optional[bool] = None

class SearchKeywordToolCall(BaseModel):
    tool_name: typing_extensions.Literal['search_keyword']
    parameters: "SearchKeywordParams"

class SearchKeywordToolCallWithoutProjectName(BaseModel):
    tool_name: typing_extensions.Literal['search_keyword']
    parameters: "SearchKeywordParamsWithoutProjectName"

class SemanticSearchParams(BaseModel):
    query: str
    project_name: typing.Optional[str] = None
    fetch_next_chunk: typing.Optional[bool] = None

class SemanticSearchToolCall(BaseModel):
    tool_name: typing_extensions.Literal['semantic_search']
    parameters: "SemanticSearchParams"

class SutraMemoryParams(BaseModel):
    add_history: str
    tasks: typing.Optional[typing.List["TaskOperation"]] = None
    code: typing.Optional[typing.List["CodeStorage"]] = None
    files: typing.Optional["FileChanges"] = None

class SutraMemoryParams_CrossIndexing(BaseModel):
    add_history: str
    tasks: typing.Optional[typing.List["TaskOperation_CrossIndexing"]] = None
    code: typing.Optional[typing.List["CodeStorage_CrossIndexing"]] = None

class SystemInfoParams(BaseModel):
    os: str
    shell: str
    home: str
    current_dir: str

class SystemInfo_CrossIndexing(BaseModel):
    home: str
    current_dir: str

class TaskFilterResponse(BaseModel):
    tasks: typing.List["AddTask"]

class TaskOperation(BaseModel):
    action: TaskOperationAction
    id: str
    from_status: typing.Optional[TaskStatus] = None
    to_status: typing.Optional[TaskStatus] = None
    description: typing.Optional[str] = None

class TaskOperation_CrossIndexing(BaseModel):
    action: TaskOperationAction_CrossIndexing
    id: str
    from_status: typing.Optional[Status_CrossIndexing] = None
    to_status: typing.Optional[Status_CrossIndexing] = None
    description: typing.Optional[str] = None

class TechnologyCorrection(BaseModel):
    original_name: str
    corrected_name: str

class TechnologyCorrectionResponse(BaseModel):
    corrections: typing.Optional[typing.List["TechnologyCorrection"]] = None

# #########################################################################
# Generated type aliases (2)
# #########################################################################


RoadmapToolCall: typing_extensions.TypeAlias = typing.Union["DatabaseToolCall", "SearchKeywordToolCall", "SemanticSearchToolCall", "ListFilesToolCall", "RoadmapCompletionToolCall"]


ToolCall_CrossIndexing: typing_extensions.TypeAlias = typing.Union["ListFilesToolCallWithoutProjectName", "DatabaseToolCall", "SearchKeywordToolCallWithoutProjectName", "CompletionToolCall_CrossIndexing"]

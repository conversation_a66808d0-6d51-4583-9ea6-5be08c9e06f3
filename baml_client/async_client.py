# ----------------------------------------------------------------------------
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml
#
# ----------------------------------------------------------------------------

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code using: baml-cli generate
# baml-cli is available with the baml package.

import typing
import typing_extensions
import baml_py

from . import stream_types, types, type_builder
from .parser import LlmResponseParser, LlmStreamParser
from .runtime import DoNotUseDirectlyCallManager, BamlCallOptions
from .globals import DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME as __runtime__


class BamlAsyncClient:
    __options: DoNotUseDirectlyCallManager
    __stream_client: "BamlStreamClient"
    __http_request: "BamlHttpRequestClient"
    __http_stream_request: "BamlHttpStreamRequestClient"
    __llm_response_parser: LlmResponseParser
    __llm_stream_parser: LlmStreamParser

    def __init__(self, options: DoNotUseDirectlyCallManager):
        self.__options = options
        self.__stream_client = BamlStreamClient(options)
        self.__http_request = BamlHttpRequestClient(options)
        self.__http_stream_request = BamlHttpStreamRequestClient(options)
        self.__llm_response_parser = LlmResponseParser(options)
        self.__llm_stream_parser = LlmStreamParser(options)

    def with_options(self,
        tb: typing.Optional[type_builder.TypeBuilder] = None,
        client_registry: typing.Optional[baml_py.baml_py.ClientRegistry] = None,
        collector: typing.Optional[typing.Union[baml_py.baml_py.Collector, typing.List[baml_py.baml_py.Collector]]] = None,
        env: typing.Optional[typing.Dict[str, typing.Optional[str]]] = None,
        on_tick: typing.Optional[typing.Callable[[str, baml_py.baml_py.FunctionLog], None]] = None,
    ) -> "BamlAsyncClient":
        options: BamlCallOptions = {}
        if tb is not None:
            options["tb"] = tb
        if client_registry is not None:
            options["client_registry"] = client_registry
        if collector is not None:
            options["collector"] = collector
        if env is not None:
            options["env"] = env
        if on_tick is not None:
            options["on_tick"] = on_tick
        return BamlAsyncClient(self.__options.merge_options(options))

    @property
    def stream(self):
      return self.__stream_client

    @property
    def request(self):
      return self.__http_request

    @property
    def stream_request(self):
      return self.__http_stream_request

    @property
    def parse(self):
      return self.__llm_response_parser

    @property
    def parse_stream(self):
      return self.__llm_stream_parser

    async def AnthropicCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CodeManagerResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.AnthropicCodeManager(tool_results=tool_results,system_info=system_info,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="AnthropicCodeManager", args={
                "tool_results": tool_results,"system_info": system_info,
            })
            return typing.cast(types.CodeManagerResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def AnthropicConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> types.ConnectionMatchingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.AnthropicConnectionMatching(incoming_connections=incoming_connections,outgoing_connections=outgoing_connections,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="AnthropicConnectionMatching", args={
                "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
            })
            return typing.cast(types.ConnectionMatchingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def AnthropicConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> types.ConnectionSplittingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.AnthropicConnectionSplitting(memory_context=memory_context,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="AnthropicConnectionSplitting", args={
                "memory_context": memory_context,
            })
            return typing.cast(types.ConnectionSplittingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def AnthropicImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.AnthropicImplementationDiscovery(analysis_query=analysis_query,memory_context=memory_context,system_info=system_info,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="AnthropicImplementationDiscovery", args={
                "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
            })
            return typing.cast(types.CrossIndexingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def AnthropicImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.AnthropicImportDiscovery(analysis_query=analysis_query,memory_context=memory_context,system_info=system_info,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="AnthropicImportDiscovery", args={
                "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
            })
            return typing.cast(types.CrossIndexingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def AnthropicPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.AnthropicPackageDiscovery(analysis_query=analysis_query,memory_context=memory_context,system_info=system_info,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="AnthropicPackageDiscovery", args={
                "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
            })
            return typing.cast(types.CrossIndexingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def AnthropicRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> types.RoadmapResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.AnthropicRoadmapAgent(params=params,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="AnthropicRoadmapAgent", args={
                "params": params,
            })
            return typing.cast(types.RoadmapResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def AnthropicTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> types.TaskFilterResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.AnthropicTaskFilter(task_list=task_list,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="AnthropicTaskFilter", args={
                "task_list": task_list,
            })
            return typing.cast(types.TaskFilterResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def AnthropicTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> types.TechnologyCorrectionResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.AnthropicTechnologyCorrection(unmatched_names=unmatched_names,acceptable_enums=acceptable_enums,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="AnthropicTechnologyCorrection", args={
                "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
            })
            return typing.cast(types.TechnologyCorrectionResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def AwsCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CodeManagerResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.AwsCodeManager(tool_results=tool_results,system_info=system_info,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="AwsCodeManager", args={
                "tool_results": tool_results,"system_info": system_info,
            })
            return typing.cast(types.CodeManagerResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def AwsConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> types.ConnectionMatchingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.AwsConnectionMatching(incoming_connections=incoming_connections,outgoing_connections=outgoing_connections,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="AwsConnectionMatching", args={
                "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
            })
            return typing.cast(types.ConnectionMatchingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def AwsConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> types.ConnectionSplittingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.AwsConnectionSplitting(memory_context=memory_context,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="AwsConnectionSplitting", args={
                "memory_context": memory_context,
            })
            return typing.cast(types.ConnectionSplittingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def AwsImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.AwsImplementationDiscovery(analysis_query=analysis_query,memory_context=memory_context,system_info=system_info,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="AwsImplementationDiscovery", args={
                "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
            })
            return typing.cast(types.CrossIndexingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def AwsImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.AwsImportDiscovery(analysis_query=analysis_query,memory_context=memory_context,system_info=system_info,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="AwsImportDiscovery", args={
                "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
            })
            return typing.cast(types.CrossIndexingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def AwsPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.AwsPackageDiscovery(analysis_query=analysis_query,memory_context=memory_context,system_info=system_info,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="AwsPackageDiscovery", args={
                "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
            })
            return typing.cast(types.CrossIndexingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def AwsRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> types.RoadmapResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.AwsRoadmapAgent(params=params,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="AwsRoadmapAgent", args={
                "params": params,
            })
            return typing.cast(types.RoadmapResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def AwsTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> types.TaskFilterResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.AwsTaskFilter(task_list=task_list,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="AwsTaskFilter", args={
                "task_list": task_list,
            })
            return typing.cast(types.TaskFilterResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def AwsTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> types.TechnologyCorrectionResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.AwsTechnologyCorrection(unmatched_names=unmatched_names,acceptable_enums=acceptable_enums,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="AwsTechnologyCorrection", args={
                "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
            })
            return typing.cast(types.TechnologyCorrectionResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def ChatGPTCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CodeManagerResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.ChatGPTCodeManager(tool_results=tool_results,system_info=system_info,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="ChatGPTCodeManager", args={
                "tool_results": tool_results,"system_info": system_info,
            })
            return typing.cast(types.CodeManagerResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def ChatGPTConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> types.ConnectionMatchingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.ChatGPTConnectionMatching(incoming_connections=incoming_connections,outgoing_connections=outgoing_connections,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="ChatGPTConnectionMatching", args={
                "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
            })
            return typing.cast(types.ConnectionMatchingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def ChatGPTConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> types.ConnectionSplittingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.ChatGPTConnectionSplitting(memory_context=memory_context,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="ChatGPTConnectionSplitting", args={
                "memory_context": memory_context,
            })
            return typing.cast(types.ConnectionSplittingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def ChatGPTImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.ChatGPTImplementationDiscovery(analysis_query=analysis_query,memory_context=memory_context,system_info=system_info,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="ChatGPTImplementationDiscovery", args={
                "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
            })
            return typing.cast(types.CrossIndexingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def ChatGPTImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.ChatGPTImportDiscovery(analysis_query=analysis_query,memory_context=memory_context,system_info=system_info,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="ChatGPTImportDiscovery", args={
                "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
            })
            return typing.cast(types.CrossIndexingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def ChatGPTPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.ChatGPTPackageDiscovery(analysis_query=analysis_query,memory_context=memory_context,system_info=system_info,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="ChatGPTPackageDiscovery", args={
                "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
            })
            return typing.cast(types.CrossIndexingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def ChatGPTRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> types.RoadmapResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.ChatGPTRoadmapAgent(params=params,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="ChatGPTRoadmapAgent", args={
                "params": params,
            })
            return typing.cast(types.RoadmapResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def ChatGPTTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> types.TaskFilterResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.ChatGPTTaskFilter(task_list=task_list,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="ChatGPTTaskFilter", args={
                "task_list": task_list,
            })
            return typing.cast(types.TaskFilterResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    async def ChatGPTTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> types.TechnologyCorrectionResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            # Use streaming internally when on_tick is provided
            stream = self.stream.ChatGPTTechnologyCorrection(unmatched_names=unmatched_names,acceptable_enums=acceptable_enums,
                baml_options=baml_options)
            return await stream.get_final_response()
        else:
            # Original non-streaming code
            result = await self.__options.merge_options(baml_options).call_function_async(function_name="ChatGPTTechnologyCorrection", args={
                "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
            })
            return typing.cast(types.TechnologyCorrectionResponse, result.cast_to(types, types, stream_types, False, __runtime__))



class BamlStreamClient:
    __options: DoNotUseDirectlyCallManager

    def __init__(self, options: DoNotUseDirectlyCallManager):
        self.__options = options

    def AnthropicCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.CodeManagerResponse, types.CodeManagerResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="AnthropicCodeManager", args={
            "tool_results": tool_results,"system_info": system_info,
        })
        return baml_py.BamlStream[stream_types.CodeManagerResponse, types.CodeManagerResponse](
          result,
          lambda x: typing.cast(stream_types.CodeManagerResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CodeManagerResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AnthropicConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.ConnectionMatchingResponse, types.ConnectionMatchingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="AnthropicConnectionMatching", args={
            "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
        })
        return baml_py.BamlStream[stream_types.ConnectionMatchingResponse, types.ConnectionMatchingResponse](
          result,
          lambda x: typing.cast(stream_types.ConnectionMatchingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.ConnectionMatchingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AnthropicConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.ConnectionSplittingResponse, types.ConnectionSplittingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="AnthropicConnectionSplitting", args={
            "memory_context": memory_context,
        })
        return baml_py.BamlStream[stream_types.ConnectionSplittingResponse, types.ConnectionSplittingResponse](
          result,
          lambda x: typing.cast(stream_types.ConnectionSplittingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.ConnectionSplittingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AnthropicImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="AnthropicImplementationDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        })
        return baml_py.BamlStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse](
          result,
          lambda x: typing.cast(stream_types.CrossIndexingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CrossIndexingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AnthropicImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="AnthropicImportDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        })
        return baml_py.BamlStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse](
          result,
          lambda x: typing.cast(stream_types.CrossIndexingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CrossIndexingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AnthropicPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="AnthropicPackageDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        })
        return baml_py.BamlStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse](
          result,
          lambda x: typing.cast(stream_types.CrossIndexingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CrossIndexingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AnthropicRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.RoadmapResponse, types.RoadmapResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="AnthropicRoadmapAgent", args={
            "params": params,
        })
        return baml_py.BamlStream[stream_types.RoadmapResponse, types.RoadmapResponse](
          result,
          lambda x: typing.cast(stream_types.RoadmapResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.RoadmapResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AnthropicTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.TaskFilterResponse, types.TaskFilterResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="AnthropicTaskFilter", args={
            "task_list": task_list,
        })
        return baml_py.BamlStream[stream_types.TaskFilterResponse, types.TaskFilterResponse](
          result,
          lambda x: typing.cast(stream_types.TaskFilterResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.TaskFilterResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AnthropicTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.TechnologyCorrectionResponse, types.TechnologyCorrectionResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="AnthropicTechnologyCorrection", args={
            "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
        })
        return baml_py.BamlStream[stream_types.TechnologyCorrectionResponse, types.TechnologyCorrectionResponse](
          result,
          lambda x: typing.cast(stream_types.TechnologyCorrectionResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.TechnologyCorrectionResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AwsCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.CodeManagerResponse, types.CodeManagerResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="AwsCodeManager", args={
            "tool_results": tool_results,"system_info": system_info,
        })
        return baml_py.BamlStream[stream_types.CodeManagerResponse, types.CodeManagerResponse](
          result,
          lambda x: typing.cast(stream_types.CodeManagerResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CodeManagerResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AwsConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.ConnectionMatchingResponse, types.ConnectionMatchingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="AwsConnectionMatching", args={
            "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
        })
        return baml_py.BamlStream[stream_types.ConnectionMatchingResponse, types.ConnectionMatchingResponse](
          result,
          lambda x: typing.cast(stream_types.ConnectionMatchingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.ConnectionMatchingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AwsConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.ConnectionSplittingResponse, types.ConnectionSplittingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="AwsConnectionSplitting", args={
            "memory_context": memory_context,
        })
        return baml_py.BamlStream[stream_types.ConnectionSplittingResponse, types.ConnectionSplittingResponse](
          result,
          lambda x: typing.cast(stream_types.ConnectionSplittingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.ConnectionSplittingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AwsImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="AwsImplementationDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        })
        return baml_py.BamlStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse](
          result,
          lambda x: typing.cast(stream_types.CrossIndexingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CrossIndexingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AwsImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="AwsImportDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        })
        return baml_py.BamlStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse](
          result,
          lambda x: typing.cast(stream_types.CrossIndexingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CrossIndexingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AwsPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="AwsPackageDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        })
        return baml_py.BamlStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse](
          result,
          lambda x: typing.cast(stream_types.CrossIndexingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CrossIndexingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AwsRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.RoadmapResponse, types.RoadmapResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="AwsRoadmapAgent", args={
            "params": params,
        })
        return baml_py.BamlStream[stream_types.RoadmapResponse, types.RoadmapResponse](
          result,
          lambda x: typing.cast(stream_types.RoadmapResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.RoadmapResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AwsTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.TaskFilterResponse, types.TaskFilterResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="AwsTaskFilter", args={
            "task_list": task_list,
        })
        return baml_py.BamlStream[stream_types.TaskFilterResponse, types.TaskFilterResponse](
          result,
          lambda x: typing.cast(stream_types.TaskFilterResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.TaskFilterResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AwsTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.TechnologyCorrectionResponse, types.TechnologyCorrectionResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="AwsTechnologyCorrection", args={
            "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
        })
        return baml_py.BamlStream[stream_types.TechnologyCorrectionResponse, types.TechnologyCorrectionResponse](
          result,
          lambda x: typing.cast(stream_types.TechnologyCorrectionResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.TechnologyCorrectionResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def ChatGPTCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.CodeManagerResponse, types.CodeManagerResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="ChatGPTCodeManager", args={
            "tool_results": tool_results,"system_info": system_info,
        })
        return baml_py.BamlStream[stream_types.CodeManagerResponse, types.CodeManagerResponse](
          result,
          lambda x: typing.cast(stream_types.CodeManagerResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CodeManagerResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def ChatGPTConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.ConnectionMatchingResponse, types.ConnectionMatchingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="ChatGPTConnectionMatching", args={
            "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
        })
        return baml_py.BamlStream[stream_types.ConnectionMatchingResponse, types.ConnectionMatchingResponse](
          result,
          lambda x: typing.cast(stream_types.ConnectionMatchingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.ConnectionMatchingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def ChatGPTConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.ConnectionSplittingResponse, types.ConnectionSplittingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="ChatGPTConnectionSplitting", args={
            "memory_context": memory_context,
        })
        return baml_py.BamlStream[stream_types.ConnectionSplittingResponse, types.ConnectionSplittingResponse](
          result,
          lambda x: typing.cast(stream_types.ConnectionSplittingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.ConnectionSplittingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def ChatGPTImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="ChatGPTImplementationDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        })
        return baml_py.BamlStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse](
          result,
          lambda x: typing.cast(stream_types.CrossIndexingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CrossIndexingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def ChatGPTImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="ChatGPTImportDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        })
        return baml_py.BamlStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse](
          result,
          lambda x: typing.cast(stream_types.CrossIndexingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CrossIndexingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def ChatGPTPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="ChatGPTPackageDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        })
        return baml_py.BamlStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse](
          result,
          lambda x: typing.cast(stream_types.CrossIndexingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CrossIndexingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def ChatGPTRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.RoadmapResponse, types.RoadmapResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="ChatGPTRoadmapAgent", args={
            "params": params,
        })
        return baml_py.BamlStream[stream_types.RoadmapResponse, types.RoadmapResponse](
          result,
          lambda x: typing.cast(stream_types.RoadmapResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.RoadmapResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def ChatGPTTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.TaskFilterResponse, types.TaskFilterResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="ChatGPTTaskFilter", args={
            "task_list": task_list,
        })
        return baml_py.BamlStream[stream_types.TaskFilterResponse, types.TaskFilterResponse](
          result,
          lambda x: typing.cast(stream_types.TaskFilterResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.TaskFilterResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def ChatGPTTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlStream[stream_types.TechnologyCorrectionResponse, types.TechnologyCorrectionResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_async_stream(function_name="ChatGPTTechnologyCorrection", args={
            "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
        })
        return baml_py.BamlStream[stream_types.TechnologyCorrectionResponse, types.TechnologyCorrectionResponse](
          result,
          lambda x: typing.cast(stream_types.TechnologyCorrectionResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.TechnologyCorrectionResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )


class BamlHttpRequestClient:
    __options: DoNotUseDirectlyCallManager

    def __init__(self, options: DoNotUseDirectlyCallManager):
        self.__options = options

    async def AnthropicCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AnthropicCodeManager", args={
            "tool_results": tool_results,"system_info": system_info,
        }, mode="request")
        return result
    async def AnthropicConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AnthropicConnectionMatching", args={
            "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
        }, mode="request")
        return result
    async def AnthropicConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AnthropicConnectionSplitting", args={
            "memory_context": memory_context,
        }, mode="request")
        return result
    async def AnthropicImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AnthropicImplementationDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="request")
        return result
    async def AnthropicImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AnthropicImportDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="request")
        return result
    async def AnthropicPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AnthropicPackageDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="request")
        return result
    async def AnthropicRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AnthropicRoadmapAgent", args={
            "params": params,
        }, mode="request")
        return result
    async def AnthropicTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AnthropicTaskFilter", args={
            "task_list": task_list,
        }, mode="request")
        return result
    async def AnthropicTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AnthropicTechnologyCorrection", args={
            "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
        }, mode="request")
        return result
    async def AwsCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AwsCodeManager", args={
            "tool_results": tool_results,"system_info": system_info,
        }, mode="request")
        return result
    async def AwsConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AwsConnectionMatching", args={
            "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
        }, mode="request")
        return result
    async def AwsConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AwsConnectionSplitting", args={
            "memory_context": memory_context,
        }, mode="request")
        return result
    async def AwsImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AwsImplementationDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="request")
        return result
    async def AwsImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AwsImportDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="request")
        return result
    async def AwsPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AwsPackageDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="request")
        return result
    async def AwsRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AwsRoadmapAgent", args={
            "params": params,
        }, mode="request")
        return result
    async def AwsTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AwsTaskFilter", args={
            "task_list": task_list,
        }, mode="request")
        return result
    async def AwsTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AwsTechnologyCorrection", args={
            "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
        }, mode="request")
        return result
    async def ChatGPTCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="ChatGPTCodeManager", args={
            "tool_results": tool_results,"system_info": system_info,
        }, mode="request")
        return result
    async def ChatGPTConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="ChatGPTConnectionMatching", args={
            "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
        }, mode="request")
        return result
    async def ChatGPTConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="ChatGPTConnectionSplitting", args={
            "memory_context": memory_context,
        }, mode="request")
        return result
    async def ChatGPTImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="ChatGPTImplementationDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="request")
        return result
    async def ChatGPTImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="ChatGPTImportDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="request")
        return result
    async def ChatGPTPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="ChatGPTPackageDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="request")
        return result
    async def ChatGPTRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="ChatGPTRoadmapAgent", args={
            "params": params,
        }, mode="request")
        return result
    async def ChatGPTTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="ChatGPTTaskFilter", args={
            "task_list": task_list,
        }, mode="request")
        return result
    async def ChatGPTTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="ChatGPTTechnologyCorrection", args={
            "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
        }, mode="request")
        return result


class BamlHttpStreamRequestClient:
    __options: DoNotUseDirectlyCallManager

    def __init__(self, options: DoNotUseDirectlyCallManager):
        self.__options = options

    async def AnthropicCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AnthropicCodeManager", args={
            "tool_results": tool_results,"system_info": system_info,
        }, mode="stream")
        return result
    async def AnthropicConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AnthropicConnectionMatching", args={
            "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
        }, mode="stream")
        return result
    async def AnthropicConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AnthropicConnectionSplitting", args={
            "memory_context": memory_context,
        }, mode="stream")
        return result
    async def AnthropicImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AnthropicImplementationDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="stream")
        return result
    async def AnthropicImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AnthropicImportDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="stream")
        return result
    async def AnthropicPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AnthropicPackageDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="stream")
        return result
    async def AnthropicRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AnthropicRoadmapAgent", args={
            "params": params,
        }, mode="stream")
        return result
    async def AnthropicTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AnthropicTaskFilter", args={
            "task_list": task_list,
        }, mode="stream")
        return result
    async def AnthropicTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AnthropicTechnologyCorrection", args={
            "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
        }, mode="stream")
        return result
    async def AwsCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AwsCodeManager", args={
            "tool_results": tool_results,"system_info": system_info,
        }, mode="stream")
        return result
    async def AwsConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AwsConnectionMatching", args={
            "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
        }, mode="stream")
        return result
    async def AwsConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AwsConnectionSplitting", args={
            "memory_context": memory_context,
        }, mode="stream")
        return result
    async def AwsImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AwsImplementationDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="stream")
        return result
    async def AwsImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AwsImportDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="stream")
        return result
    async def AwsPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AwsPackageDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="stream")
        return result
    async def AwsRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AwsRoadmapAgent", args={
            "params": params,
        }, mode="stream")
        return result
    async def AwsTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AwsTaskFilter", args={
            "task_list": task_list,
        }, mode="stream")
        return result
    async def AwsTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="AwsTechnologyCorrection", args={
            "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
        }, mode="stream")
        return result
    async def ChatGPTCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="ChatGPTCodeManager", args={
            "tool_results": tool_results,"system_info": system_info,
        }, mode="stream")
        return result
    async def ChatGPTConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="ChatGPTConnectionMatching", args={
            "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
        }, mode="stream")
        return result
    async def ChatGPTConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="ChatGPTConnectionSplitting", args={
            "memory_context": memory_context,
        }, mode="stream")
        return result
    async def ChatGPTImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="ChatGPTImplementationDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="stream")
        return result
    async def ChatGPTImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="ChatGPTImportDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="stream")
        return result
    async def ChatGPTPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="ChatGPTPackageDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="stream")
        return result
    async def ChatGPTRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="ChatGPTRoadmapAgent", args={
            "params": params,
        }, mode="stream")
        return result
    async def ChatGPTTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="ChatGPTTaskFilter", args={
            "task_list": task_list,
        }, mode="stream")
        return result
    async def ChatGPTTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = await self.__options.merge_options(baml_options).create_http_request_async(function_name="ChatGPTTechnologyCorrection", args={
            "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
        }, mode="stream")
        return result


b = BamlAsyncClient(DoNotUseDirectlyCallManager({}))

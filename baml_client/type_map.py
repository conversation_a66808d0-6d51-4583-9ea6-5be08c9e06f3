# ----------------------------------------------------------------------------
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml
#
# ----------------------------------------------------------------------------

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code using: baml-cli generate
# baml-cli is available with the baml package.

from . import types
from . import stream_types


type_map = {

    "types.AddTask": types.AddTask,
    "stream_types.AddTask": stream_types.AddTask,

    "types.BaseCompletionParams": types.BaseCompletionParams,
    "stream_types.BaseCompletionParams": stream_types.BaseCompletionParams,

    "types.BasePromptParams": types.BasePromptParams,
    "stream_types.BasePromptParams": stream_types.BasePromptParams,

    "types.ChangeInstruction": types.ChangeInstruction,
    "stream_types.ChangeInstruction": stream_types.ChangeInstruction,

    "types.CodeConnection": types.CodeConnection,
    "stream_types.CodeConnection": stream_types.CodeConnection,

    "types.CodeManagerResponse": types.CodeManagerResponse,
    "stream_types.CodeManagerResponse": stream_types.CodeManagerResponse,

    "types.CodeStorage": types.CodeStorage,
    "stream_types.CodeStorage": stream_types.CodeStorage,

    "types.CodeStorage_CrossIndexing": types.CodeStorage_CrossIndexing,
    "stream_types.CodeStorage_CrossIndexing": stream_types.CodeStorage_CrossIndexing,

    "types.CompletionResponse_CrossIndexing": types.CompletionResponse_CrossIndexing,
    "stream_types.CompletionResponse_CrossIndexing": stream_types.CompletionResponse_CrossIndexing,

    "types.CompletionToolCall": types.CompletionToolCall,
    "stream_types.CompletionToolCall": stream_types.CompletionToolCall,

    "types.CompletionToolCall_CrossIndexing": types.CompletionToolCall_CrossIndexing,
    "stream_types.CompletionToolCall_CrossIndexing": stream_types.CompletionToolCall_CrossIndexing,

    "types.ConnectionDetail": types.ConnectionDetail,
    "stream_types.ConnectionDetail": stream_types.ConnectionDetail,

    "types.ConnectionMatch": types.ConnectionMatch,
    "stream_types.ConnectionMatch": stream_types.ConnectionMatch,

    "types.ConnectionMatchingResponse": types.ConnectionMatchingResponse,
    "stream_types.ConnectionMatchingResponse": stream_types.ConnectionMatchingResponse,

    "types.ConnectionSplittingResponse": types.ConnectionSplittingResponse,
    "stream_types.ConnectionSplittingResponse": stream_types.ConnectionSplittingResponse,

    "types.Contract": types.Contract,
    "stream_types.Contract": stream_types.Contract,

    "types.ContractField": types.ContractField,
    "stream_types.ContractField": stream_types.ContractField,

    "types.CrossIndexingResponse": types.CrossIndexingResponse,
    "stream_types.CrossIndexingResponse": stream_types.CrossIndexingResponse,

    "types.DatabaseParams": types.DatabaseParams,
    "stream_types.DatabaseParams": stream_types.DatabaseParams,

    "types.DatabaseToolCall": types.DatabaseToolCall,
    "stream_types.DatabaseToolCall": stream_types.DatabaseToolCall,

    "types.FileChange": types.FileChange,
    "stream_types.FileChange": stream_types.FileChange,

    "types.FileChanges": types.FileChanges,
    "stream_types.FileChanges": stream_types.FileChanges,

    "types.ListFilesParams": types.ListFilesParams,
    "stream_types.ListFilesParams": stream_types.ListFilesParams,

    "types.ListFilesParamsWithoutProjectName": types.ListFilesParamsWithoutProjectName,
    "stream_types.ListFilesParamsWithoutProjectName": stream_types.ListFilesParamsWithoutProjectName,

    "types.ListFilesToolCall": types.ListFilesToolCall,
    "stream_types.ListFilesToolCall": stream_types.ListFilesToolCall,

    "types.ListFilesToolCallWithoutProjectName": types.ListFilesToolCallWithoutProjectName,
    "stream_types.ListFilesToolCallWithoutProjectName": stream_types.ListFilesToolCallWithoutProjectName,

    "types.Project": types.Project,
    "stream_types.Project": stream_types.Project,

    "types.ProjectContext": types.ProjectContext,
    "stream_types.ProjectContext": stream_types.ProjectContext,

    "types.ProjectRoadmap": types.ProjectRoadmap,
    "stream_types.ProjectRoadmap": stream_types.ProjectRoadmap,

    "types.RoadmapAgentParams": types.RoadmapAgentParams,
    "stream_types.RoadmapAgentParams": stream_types.RoadmapAgentParams,

    "types.RoadmapCompletionParams": types.RoadmapCompletionParams,
    "stream_types.RoadmapCompletionParams": stream_types.RoadmapCompletionParams,

    "types.RoadmapCompletionToolCall": types.RoadmapCompletionToolCall,
    "stream_types.RoadmapCompletionToolCall": stream_types.RoadmapCompletionToolCall,

    "types.RoadmapPromptParams": types.RoadmapPromptParams,
    "stream_types.RoadmapPromptParams": stream_types.RoadmapPromptParams,

    "types.RoadmapResponse": types.RoadmapResponse,
    "stream_types.RoadmapResponse": stream_types.RoadmapResponse,

    "types.SearchKeywordParams": types.SearchKeywordParams,
    "stream_types.SearchKeywordParams": stream_types.SearchKeywordParams,

    "types.SearchKeywordParamsWithoutProjectName": types.SearchKeywordParamsWithoutProjectName,
    "stream_types.SearchKeywordParamsWithoutProjectName": stream_types.SearchKeywordParamsWithoutProjectName,

    "types.SearchKeywordToolCall": types.SearchKeywordToolCall,
    "stream_types.SearchKeywordToolCall": stream_types.SearchKeywordToolCall,

    "types.SearchKeywordToolCallWithoutProjectName": types.SearchKeywordToolCallWithoutProjectName,
    "stream_types.SearchKeywordToolCallWithoutProjectName": stream_types.SearchKeywordToolCallWithoutProjectName,

    "types.SemanticSearchParams": types.SemanticSearchParams,
    "stream_types.SemanticSearchParams": stream_types.SemanticSearchParams,

    "types.SemanticSearchToolCall": types.SemanticSearchToolCall,
    "stream_types.SemanticSearchToolCall": stream_types.SemanticSearchToolCall,

    "types.SutraMemoryParams": types.SutraMemoryParams,
    "stream_types.SutraMemoryParams": stream_types.SutraMemoryParams,

    "types.SutraMemoryParams_CrossIndexing": types.SutraMemoryParams_CrossIndexing,
    "stream_types.SutraMemoryParams_CrossIndexing": stream_types.SutraMemoryParams_CrossIndexing,

    "types.SystemInfoParams": types.SystemInfoParams,
    "stream_types.SystemInfoParams": stream_types.SystemInfoParams,

    "types.SystemInfo_CrossIndexing": types.SystemInfo_CrossIndexing,
    "stream_types.SystemInfo_CrossIndexing": stream_types.SystemInfo_CrossIndexing,

    "types.TaskFilterResponse": types.TaskFilterResponse,
    "stream_types.TaskFilterResponse": stream_types.TaskFilterResponse,

    "types.TaskOperation": types.TaskOperation,
    "stream_types.TaskOperation": stream_types.TaskOperation,

    "types.TaskOperation_CrossIndexing": types.TaskOperation_CrossIndexing,
    "stream_types.TaskOperation_CrossIndexing": stream_types.TaskOperation_CrossIndexing,

    "types.TechnologyCorrection": types.TechnologyCorrection,
    "stream_types.TechnologyCorrection": stream_types.TechnologyCorrection,

    "types.TechnologyCorrectionResponse": types.TechnologyCorrectionResponse,
    "stream_types.TechnologyCorrectionResponse": stream_types.TechnologyCorrectionResponse,


    "types.Agent": types.Agent,

    "types.CodeStorageAction": types.CodeStorageAction,

    "types.CodeStorageAction_CrossIndexing": types.CodeStorageAction_CrossIndexing,

    "types.ContractRole": types.ContractRole,

    "types.FileOperation": types.FileOperation,

    "types.ImpactLevel": types.ImpactLevel,

    "types.Status_CrossIndexing": types.Status_CrossIndexing,

    "types.TaskOperationAction": types.TaskOperationAction,

    "types.TaskOperationAction_CrossIndexing": types.TaskOperationAction_CrossIndexing,

    "types.TaskStatus": types.TaskStatus,

    "types.TechnologyType": types.TechnologyType,

    "types.ToolName": types.ToolName,

}

# ----------------------------------------------------------------------------
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml
#
# ----------------------------------------------------------------------------

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code using: baml-cli generate
# baml-cli is available with the baml package.

import typing
from baml_py import type_builder
from baml_py import baml_py
# These are exports, not used here, hence the linter is disabled
from baml_py.baml_py import FieldType, EnumValueBuilder, EnumBuilder, ClassBuilder # noqa: F401 # pylint: disable=unused-import
from .globals import DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME

class TypeBuilder(type_builder.TypeBuilder):
    def __init__(self):
        super().__init__(classes=set(
          ["AddTask","BaseCompletionParams","BasePromptParams","ChangeInstruction","CodeConnection","CodeManagerResponse","CodeStorage","CodeStorage_CrossIndexing","CompletionResponse_CrossIndexing","CompletionToolCall","CompletionToolCall_CrossIndexing","ConnectionDetail","ConnectionMatch","ConnectionMatchingResponse","ConnectionSplittingResponse","Contract","ContractField","CrossIndexingResponse","DatabaseParams","DatabaseToolCall","FileChange","FileChanges","ListFilesParams","ListFilesParamsWithoutProjectName","ListFilesToolCall","ListFilesToolCallWithoutProjectName","Project","ProjectContext","ProjectRoadmap","RoadmapAgentParams","RoadmapCompletionParams","RoadmapCompletionToolCall","RoadmapPromptParams","RoadmapResponse","SearchKeywordParams","SearchKeywordParamsWithoutProjectName","SearchKeywordToolCall","SearchKeywordToolCallWithoutProjectName","SemanticSearchParams","SemanticSearchToolCall","SutraMemoryParams","SutraMemoryParams_CrossIndexing","SystemInfoParams","SystemInfo_CrossIndexing","TaskFilterResponse","TaskOperation","TaskOperation_CrossIndexing","TechnologyCorrection","TechnologyCorrectionResponse",]
        ), enums=set(
          ["Agent","CodeStorageAction","CodeStorageAction_CrossIndexing","ContractRole","FileOperation","ImpactLevel","Status_CrossIndexing","TaskOperationAction","TaskOperationAction_CrossIndexing","TaskStatus","TechnologyType","ToolName",]
        ), runtime=DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME)

    # #########################################################################
    # Generated enums 12
    # #########################################################################

    @property
    def Agent(self) -> "AgentViewer":
        return AgentViewer(self)

    @property
    def CodeStorageAction(self) -> "CodeStorageActionViewer":
        return CodeStorageActionViewer(self)

    @property
    def CodeStorageAction_CrossIndexing(self) -> "CodeStorageAction_CrossIndexingViewer":
        return CodeStorageAction_CrossIndexingViewer(self)

    @property
    def ContractRole(self) -> "ContractRoleViewer":
        return ContractRoleViewer(self)

    @property
    def FileOperation(self) -> "FileOperationViewer":
        return FileOperationViewer(self)

    @property
    def ImpactLevel(self) -> "ImpactLevelViewer":
        return ImpactLevelViewer(self)

    @property
    def Status_CrossIndexing(self) -> "Status_CrossIndexingViewer":
        return Status_CrossIndexingViewer(self)

    @property
    def TaskOperationAction(self) -> "TaskOperationActionViewer":
        return TaskOperationActionViewer(self)

    @property
    def TaskOperationAction_CrossIndexing(self) -> "TaskOperationAction_CrossIndexingViewer":
        return TaskOperationAction_CrossIndexingViewer(self)

    @property
    def TaskStatus(self) -> "TaskStatusViewer":
        return TaskStatusViewer(self)

    @property
    def TechnologyType(self) -> "TechnologyTypeBuilder":
        return TechnologyTypeBuilder(self)

    @property
    def ToolName(self) -> "ToolNameViewer":
        return ToolNameViewer(self)


    # #########################################################################
    # Generated classes 49
    # #########################################################################

    @property
    def AddTask(self) -> "AddTaskViewer":
        return AddTaskViewer(self)

    @property
    def BaseCompletionParams(self) -> "BaseCompletionParamsViewer":
        return BaseCompletionParamsViewer(self)

    @property
    def BasePromptParams(self) -> "BasePromptParamsViewer":
        return BasePromptParamsViewer(self)

    @property
    def ChangeInstruction(self) -> "ChangeInstructionViewer":
        return ChangeInstructionViewer(self)

    @property
    def CodeConnection(self) -> "CodeConnectionViewer":
        return CodeConnectionViewer(self)

    @property
    def CodeManagerResponse(self) -> "CodeManagerResponseViewer":
        return CodeManagerResponseViewer(self)

    @property
    def CodeStorage(self) -> "CodeStorageViewer":
        return CodeStorageViewer(self)

    @property
    def CodeStorage_CrossIndexing(self) -> "CodeStorage_CrossIndexingViewer":
        return CodeStorage_CrossIndexingViewer(self)

    @property
    def CompletionResponse_CrossIndexing(self) -> "CompletionResponse_CrossIndexingViewer":
        return CompletionResponse_CrossIndexingViewer(self)

    @property
    def CompletionToolCall(self) -> "CompletionToolCallViewer":
        return CompletionToolCallViewer(self)

    @property
    def CompletionToolCall_CrossIndexing(self) -> "CompletionToolCall_CrossIndexingViewer":
        return CompletionToolCall_CrossIndexingViewer(self)

    @property
    def ConnectionDetail(self) -> "ConnectionDetailViewer":
        return ConnectionDetailViewer(self)

    @property
    def ConnectionMatch(self) -> "ConnectionMatchViewer":
        return ConnectionMatchViewer(self)

    @property
    def ConnectionMatchingResponse(self) -> "ConnectionMatchingResponseViewer":
        return ConnectionMatchingResponseViewer(self)

    @property
    def ConnectionSplittingResponse(self) -> "ConnectionSplittingResponseViewer":
        return ConnectionSplittingResponseViewer(self)

    @property
    def Contract(self) -> "ContractViewer":
        return ContractViewer(self)

    @property
    def ContractField(self) -> "ContractFieldViewer":
        return ContractFieldViewer(self)

    @property
    def CrossIndexingResponse(self) -> "CrossIndexingResponseViewer":
        return CrossIndexingResponseViewer(self)

    @property
    def DatabaseParams(self) -> "DatabaseParamsViewer":
        return DatabaseParamsViewer(self)

    @property
    def DatabaseToolCall(self) -> "DatabaseToolCallViewer":
        return DatabaseToolCallViewer(self)

    @property
    def FileChange(self) -> "FileChangeViewer":
        return FileChangeViewer(self)

    @property
    def FileChanges(self) -> "FileChangesViewer":
        return FileChangesViewer(self)

    @property
    def ListFilesParams(self) -> "ListFilesParamsViewer":
        return ListFilesParamsViewer(self)

    @property
    def ListFilesParamsWithoutProjectName(self) -> "ListFilesParamsWithoutProjectNameViewer":
        return ListFilesParamsWithoutProjectNameViewer(self)

    @property
    def ListFilesToolCall(self) -> "ListFilesToolCallViewer":
        return ListFilesToolCallViewer(self)

    @property
    def ListFilesToolCallWithoutProjectName(self) -> "ListFilesToolCallWithoutProjectNameViewer":
        return ListFilesToolCallWithoutProjectNameViewer(self)

    @property
    def Project(self) -> "ProjectViewer":
        return ProjectViewer(self)

    @property
    def ProjectContext(self) -> "ProjectContextViewer":
        return ProjectContextViewer(self)

    @property
    def ProjectRoadmap(self) -> "ProjectRoadmapViewer":
        return ProjectRoadmapViewer(self)

    @property
    def RoadmapAgentParams(self) -> "RoadmapAgentParamsViewer":
        return RoadmapAgentParamsViewer(self)

    @property
    def RoadmapCompletionParams(self) -> "RoadmapCompletionParamsViewer":
        return RoadmapCompletionParamsViewer(self)

    @property
    def RoadmapCompletionToolCall(self) -> "RoadmapCompletionToolCallViewer":
        return RoadmapCompletionToolCallViewer(self)

    @property
    def RoadmapPromptParams(self) -> "RoadmapPromptParamsViewer":
        return RoadmapPromptParamsViewer(self)

    @property
    def RoadmapResponse(self) -> "RoadmapResponseViewer":
        return RoadmapResponseViewer(self)

    @property
    def SearchKeywordParams(self) -> "SearchKeywordParamsViewer":
        return SearchKeywordParamsViewer(self)

    @property
    def SearchKeywordParamsWithoutProjectName(self) -> "SearchKeywordParamsWithoutProjectNameViewer":
        return SearchKeywordParamsWithoutProjectNameViewer(self)

    @property
    def SearchKeywordToolCall(self) -> "SearchKeywordToolCallViewer":
        return SearchKeywordToolCallViewer(self)

    @property
    def SearchKeywordToolCallWithoutProjectName(self) -> "SearchKeywordToolCallWithoutProjectNameViewer":
        return SearchKeywordToolCallWithoutProjectNameViewer(self)

    @property
    def SemanticSearchParams(self) -> "SemanticSearchParamsViewer":
        return SemanticSearchParamsViewer(self)

    @property
    def SemanticSearchToolCall(self) -> "SemanticSearchToolCallViewer":
        return SemanticSearchToolCallViewer(self)

    @property
    def SutraMemoryParams(self) -> "SutraMemoryParamsViewer":
        return SutraMemoryParamsViewer(self)

    @property
    def SutraMemoryParams_CrossIndexing(self) -> "SutraMemoryParams_CrossIndexingViewer":
        return SutraMemoryParams_CrossIndexingViewer(self)

    @property
    def SystemInfoParams(self) -> "SystemInfoParamsViewer":
        return SystemInfoParamsViewer(self)

    @property
    def SystemInfo_CrossIndexing(self) -> "SystemInfo_CrossIndexingViewer":
        return SystemInfo_CrossIndexingViewer(self)

    @property
    def TaskFilterResponse(self) -> "TaskFilterResponseViewer":
        return TaskFilterResponseViewer(self)

    @property
    def TaskOperation(self) -> "TaskOperationViewer":
        return TaskOperationViewer(self)

    @property
    def TaskOperation_CrossIndexing(self) -> "TaskOperation_CrossIndexingViewer":
        return TaskOperation_CrossIndexingViewer(self)

    @property
    def TechnologyCorrection(self) -> "TechnologyCorrectionViewer":
        return TechnologyCorrectionViewer(self)

    @property
    def TechnologyCorrectionResponse(self) -> "TechnologyCorrectionResponseViewer":
        return TechnologyCorrectionResponseViewer(self)



# #########################################################################
# Generated enums 12
# #########################################################################

class AgentAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("Agent")
        self._values: typing.Set[str] = set([  "ROADMAP",  "CrossIndexing",  ])
        self._vals = AgentValues(self._bldr, self._values)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "AgentValues":
        return self._vals


class AgentViewer(AgentAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_values(self) -> typing.List[typing.Tuple[str, type_builder.EnumValueViewer]]:
        return [(name, type_builder.EnumValueViewer(self._bldr.value(name))) for name in self._values]


class AgentValues:
    def __init__(self, enum_bldr: baml_py.EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def ROADMAP(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("ROADMAP"))

    @property
    def CrossIndexing(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("CrossIndexing"))




class CodeStorageActionAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("CodeStorageAction")
        self._values: typing.Set[str] = set([  "Add",  "Remove",  ])
        self._vals = CodeStorageActionValues(self._bldr, self._values)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "CodeStorageActionValues":
        return self._vals


class CodeStorageActionViewer(CodeStorageActionAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_values(self) -> typing.List[typing.Tuple[str, type_builder.EnumValueViewer]]:
        return [(name, type_builder.EnumValueViewer(self._bldr.value(name))) for name in self._values]


class CodeStorageActionValues:
    def __init__(self, enum_bldr: baml_py.EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def Add(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Add"))

    @property
    def Remove(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Remove"))




class CodeStorageAction_CrossIndexingAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("CodeStorageAction_CrossIndexing")
        self._values: typing.Set[str] = set([  "Add",  "Remove",  ])
        self._vals = CodeStorageAction_CrossIndexingValues(self._bldr, self._values)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "CodeStorageAction_CrossIndexingValues":
        return self._vals


class CodeStorageAction_CrossIndexingViewer(CodeStorageAction_CrossIndexingAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_values(self) -> typing.List[typing.Tuple[str, type_builder.EnumValueViewer]]:
        return [(name, type_builder.EnumValueViewer(self._bldr.value(name))) for name in self._values]


class CodeStorageAction_CrossIndexingValues:
    def __init__(self, enum_bldr: baml_py.EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def Add(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Add"))

    @property
    def Remove(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Remove"))




class ContractRoleAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("ContractRole")
        self._values: typing.Set[str] = set([  "Provider",  "Consumer",  "Both",  ])
        self._vals = ContractRoleValues(self._bldr, self._values)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "ContractRoleValues":
        return self._vals


class ContractRoleViewer(ContractRoleAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_values(self) -> typing.List[typing.Tuple[str, type_builder.EnumValueViewer]]:
        return [(name, type_builder.EnumValueViewer(self._bldr.value(name))) for name in self._values]


class ContractRoleValues:
    def __init__(self, enum_bldr: baml_py.EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def Provider(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Provider"))

    @property
    def Consumer(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Consumer"))

    @property
    def Both(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Both"))




class FileOperationAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("FileOperation")
        self._values: typing.Set[str] = set([  "Create",  "Modify",  "Delete",  ])
        self._vals = FileOperationValues(self._bldr, self._values)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "FileOperationValues":
        return self._vals


class FileOperationViewer(FileOperationAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_values(self) -> typing.List[typing.Tuple[str, type_builder.EnumValueViewer]]:
        return [(name, type_builder.EnumValueViewer(self._bldr.value(name))) for name in self._values]


class FileOperationValues:
    def __init__(self, enum_bldr: baml_py.EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def Create(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Create"))

    @property
    def Modify(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Modify"))

    @property
    def Delete(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Delete"))




class ImpactLevelAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("ImpactLevel")
        self._values: typing.Set[str] = set([  "High",  "Medium",  "Low",  "NoImpact",  ])
        self._vals = ImpactLevelValues(self._bldr, self._values)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "ImpactLevelValues":
        return self._vals


class ImpactLevelViewer(ImpactLevelAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_values(self) -> typing.List[typing.Tuple[str, type_builder.EnumValueViewer]]:
        return [(name, type_builder.EnumValueViewer(self._bldr.value(name))) for name in self._values]


class ImpactLevelValues:
    def __init__(self, enum_bldr: baml_py.EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def High(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("High"))

    @property
    def Medium(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Medium"))

    @property
    def Low(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Low"))

    @property
    def NoImpact(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("NoImpact"))




class Status_CrossIndexingAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("Status_CrossIndexing")
        self._values: typing.Set[str] = set([  "Pending",  "Current",  "Completed",  ])
        self._vals = Status_CrossIndexingValues(self._bldr, self._values)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "Status_CrossIndexingValues":
        return self._vals


class Status_CrossIndexingViewer(Status_CrossIndexingAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_values(self) -> typing.List[typing.Tuple[str, type_builder.EnumValueViewer]]:
        return [(name, type_builder.EnumValueViewer(self._bldr.value(name))) for name in self._values]


class Status_CrossIndexingValues:
    def __init__(self, enum_bldr: baml_py.EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def Pending(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Pending"))

    @property
    def Current(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Current"))

    @property
    def Completed(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Completed"))




class TaskOperationActionAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("TaskOperationAction")
        self._values: typing.Set[str] = set([  "Add",  "Remove",  "Move",  ])
        self._vals = TaskOperationActionValues(self._bldr, self._values)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "TaskOperationActionValues":
        return self._vals


class TaskOperationActionViewer(TaskOperationActionAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_values(self) -> typing.List[typing.Tuple[str, type_builder.EnumValueViewer]]:
        return [(name, type_builder.EnumValueViewer(self._bldr.value(name))) for name in self._values]


class TaskOperationActionValues:
    def __init__(self, enum_bldr: baml_py.EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def Add(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Add"))

    @property
    def Remove(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Remove"))

    @property
    def Move(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Move"))




class TaskOperationAction_CrossIndexingAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("TaskOperationAction_CrossIndexing")
        self._values: typing.Set[str] = set([  "Add",  "Remove",  "Move",  ])
        self._vals = TaskOperationAction_CrossIndexingValues(self._bldr, self._values)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "TaskOperationAction_CrossIndexingValues":
        return self._vals


class TaskOperationAction_CrossIndexingViewer(TaskOperationAction_CrossIndexingAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_values(self) -> typing.List[typing.Tuple[str, type_builder.EnumValueViewer]]:
        return [(name, type_builder.EnumValueViewer(self._bldr.value(name))) for name in self._values]


class TaskOperationAction_CrossIndexingValues:
    def __init__(self, enum_bldr: baml_py.EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def Add(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Add"))

    @property
    def Remove(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Remove"))

    @property
    def Move(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Move"))




class TaskStatusAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("TaskStatus")
        self._values: typing.Set[str] = set([  "PENDING",  "CURRENT",  "COMPLETED",  ])
        self._vals = TaskStatusValues(self._bldr, self._values)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "TaskStatusValues":
        return self._vals


class TaskStatusViewer(TaskStatusAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_values(self) -> typing.List[typing.Tuple[str, type_builder.EnumValueViewer]]:
        return [(name, type_builder.EnumValueViewer(self._bldr.value(name))) for name in self._values]


class TaskStatusValues:
    def __init__(self, enum_bldr: baml_py.EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def PENDING(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("PENDING"))

    @property
    def CURRENT(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("CURRENT"))

    @property
    def COMPLETED(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("COMPLETED"))




class TechnologyTypeAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("TechnologyType")
        self._values: typing.Set[str] = set([  "HTTP_HTTPS",  "WebSockets",  "GRPC",  "GraphQL",  "MessageQueue",  "Unknown",  ])
        self._vals = TechnologyTypeValues(self._bldr, self._values)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "TechnologyTypeValues":
        return self._vals


class TechnologyTypeBuilder(TechnologyTypeAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_values(self) -> typing.List[typing.Tuple[str, baml_py.EnumValueBuilder]]:
        return [(name, self._bldr.value(name)) for name in self._values]

    def add_value(self, name: str) -> baml_py.EnumValueBuilder:
        if name in self._values:
            raise ValueError(f"Value {name} already exists.")
        return self._bldr.value(name)


class TechnologyTypeValues:
    def __init__(self, enum_bldr: baml_py.EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values # type: ignore (we know how to use this private attribute) # noqa: F821


    def __getattr__(self, name: str) -> baml_py.EnumValueBuilder:
        if name not in self.__values:
            raise AttributeError(f"Value {name} not found.")
        return self.__bldr.value(name)


    @property
    def HTTP_HTTPS(self) -> baml_py.EnumValueBuilder:
        return self.__bldr.value("HTTP_HTTPS")

    @property
    def WebSockets(self) -> baml_py.EnumValueBuilder:
        return self.__bldr.value("WebSockets")

    @property
    def GRPC(self) -> baml_py.EnumValueBuilder:
        return self.__bldr.value("GRPC")

    @property
    def GraphQL(self) -> baml_py.EnumValueBuilder:
        return self.__bldr.value("GraphQL")

    @property
    def MessageQueue(self) -> baml_py.EnumValueBuilder:
        return self.__bldr.value("MessageQueue")

    @property
    def Unknown(self) -> baml_py.EnumValueBuilder:
        return self.__bldr.value("Unknown")




class ToolNameAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.enum("ToolName")
        self._values: typing.Set[str] = set([  "Database",  "SearchKeyword",  "SearchKeywordWithoutProjectName",  "SemanticSearch",  "ListFiles",  "ListFilesWithoutProjectName",  "Completion",  ])
        self._vals = ToolNameValues(self._bldr, self._values)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def values(self) -> "ToolNameValues":
        return self._vals


class ToolNameViewer(ToolNameAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_values(self) -> typing.List[typing.Tuple[str, type_builder.EnumValueViewer]]:
        return [(name, type_builder.EnumValueViewer(self._bldr.value(name))) for name in self._values]


class ToolNameValues:
    def __init__(self, enum_bldr: baml_py.EnumBuilder, values: typing.Set[str]):
        self.__bldr = enum_bldr
        self.__values = values # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def Database(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Database"))

    @property
    def SearchKeyword(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("SearchKeyword"))

    @property
    def SearchKeywordWithoutProjectName(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("SearchKeywordWithoutProjectName"))

    @property
    def SemanticSearch(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("SemanticSearch"))

    @property
    def ListFiles(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("ListFiles"))

    @property
    def ListFilesWithoutProjectName(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("ListFilesWithoutProjectName"))

    @property
    def Completion(self) -> type_builder.EnumValueViewer:
        return type_builder.EnumValueViewer(self.__bldr.value("Completion"))





# #########################################################################
# Generated classes 49
# #########################################################################

class AddTaskAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("AddTask")
        self._properties: typing.Set[str] = set([  "id",  "description",  ])
        self._props = AddTaskProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "AddTaskProperties":
        return self._props


class AddTaskViewer(AddTaskAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class AddTaskProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def id(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("id"))

    @property
    def description(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("description"))




class BaseCompletionParamsAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("BaseCompletionParams")
        self._properties: typing.Set[str] = set([  "result",  ])
        self._props = BaseCompletionParamsProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "BaseCompletionParamsProperties":
        return self._props


class BaseCompletionParamsViewer(BaseCompletionParamsAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class BaseCompletionParamsProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def result(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("result"))




class BasePromptParamsAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("BasePromptParams")
        self._properties: typing.Set[str] = set([  "system_info",  "project_context",  ])
        self._props = BasePromptParamsProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "BasePromptParamsProperties":
        return self._props


class BasePromptParamsViewer(BasePromptParamsAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class BasePromptParamsProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def system_info(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("system_info"))

    @property
    def project_context(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("project_context"))




class ChangeInstructionAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ChangeInstruction")
        self._properties: typing.Set[str] = set([  "description",  "current_state",  "target_state",  "start_line",  "end_line",  "additional_notes",  ])
        self._props = ChangeInstructionProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ChangeInstructionProperties":
        return self._props


class ChangeInstructionViewer(ChangeInstructionAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ChangeInstructionProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def description(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("description"))

    @property
    def current_state(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("current_state"))

    @property
    def target_state(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("target_state"))

    @property
    def start_line(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("start_line"))

    @property
    def end_line(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("end_line"))

    @property
    def additional_notes(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("additional_notes"))




class CodeConnectionAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CodeConnection")
        self._properties: typing.Set[str] = set([  "id",  "file",  "start_line",  "end_line",  "description",  ])
        self._props = CodeConnectionProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CodeConnectionProperties":
        return self._props


class CodeConnectionViewer(CodeConnectionAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CodeConnectionProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def id(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("id"))

    @property
    def file(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("file"))

    @property
    def start_line(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("start_line"))

    @property
    def end_line(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("end_line"))

    @property
    def description(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("description"))




class CodeManagerResponseAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CodeManagerResponse")
        self._properties: typing.Set[str] = set([  "thinking",  "connection_code",  ])
        self._props = CodeManagerResponseProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CodeManagerResponseProperties":
        return self._props


class CodeManagerResponseViewer(CodeManagerResponseAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CodeManagerResponseProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def thinking(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("thinking"))

    @property
    def connection_code(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("connection_code"))




class CodeStorageAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CodeStorage")
        self._properties: typing.Set[str] = set([  "action",  "id",  "file",  "start_line",  "end_line",  "description",  ])
        self._props = CodeStorageProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CodeStorageProperties":
        return self._props


class CodeStorageViewer(CodeStorageAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CodeStorageProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def action(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("action"))

    @property
    def id(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("id"))

    @property
    def file(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("file"))

    @property
    def start_line(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("start_line"))

    @property
    def end_line(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("end_line"))

    @property
    def description(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("description"))




class CodeStorage_CrossIndexingAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CodeStorage_CrossIndexing")
        self._properties: typing.Set[str] = set([  "action",  "id",  "file",  "start_line",  "end_line",  "description",  ])
        self._props = CodeStorage_CrossIndexingProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CodeStorage_CrossIndexingProperties":
        return self._props


class CodeStorage_CrossIndexingViewer(CodeStorage_CrossIndexingAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CodeStorage_CrossIndexingProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def action(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("action"))

    @property
    def id(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("id"))

    @property
    def file(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("file"))

    @property
    def start_line(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("start_line"))

    @property
    def end_line(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("end_line"))

    @property
    def description(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("description"))




class CompletionResponse_CrossIndexingAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CompletionResponse_CrossIndexing")
        self._properties: typing.Set[str] = set([  "result",  ])
        self._props = CompletionResponse_CrossIndexingProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CompletionResponse_CrossIndexingProperties":
        return self._props


class CompletionResponse_CrossIndexingViewer(CompletionResponse_CrossIndexingAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CompletionResponse_CrossIndexingProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def result(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("result"))




class CompletionToolCallAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CompletionToolCall")
        self._properties: typing.Set[str] = set([  "tool_name",  "parameters",  ])
        self._props = CompletionToolCallProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CompletionToolCallProperties":
        return self._props


class CompletionToolCallViewer(CompletionToolCallAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CompletionToolCallProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def tool_name(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("tool_name"))

    @property
    def parameters(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("parameters"))




class CompletionToolCall_CrossIndexingAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CompletionToolCall_CrossIndexing")
        self._properties: typing.Set[str] = set([  "tool_name",  "parameters",  ])
        self._props = CompletionToolCall_CrossIndexingProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CompletionToolCall_CrossIndexingProperties":
        return self._props


class CompletionToolCall_CrossIndexingViewer(CompletionToolCall_CrossIndexingAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CompletionToolCall_CrossIndexingProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def tool_name(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("tool_name"))

    @property
    def parameters(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("parameters"))




class ConnectionDetailAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ConnectionDetail")
        self._properties: typing.Set[str] = set([  "snippet_lines",  "description",  ])
        self._props = ConnectionDetailProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ConnectionDetailProperties":
        return self._props


class ConnectionDetailViewer(ConnectionDetailAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ConnectionDetailProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def snippet_lines(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("snippet_lines"))

    @property
    def description(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("description"))




class ConnectionMatchAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ConnectionMatch")
        self._properties: typing.Set[str] = set([  "incoming_id",  "outgoing_id",  "match_confidence",  "match_reason",  ])
        self._props = ConnectionMatchProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ConnectionMatchProperties":
        return self._props


class ConnectionMatchViewer(ConnectionMatchAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ConnectionMatchProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def incoming_id(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("incoming_id"))

    @property
    def outgoing_id(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("outgoing_id"))

    @property
    def match_confidence(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("match_confidence"))

    @property
    def match_reason(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("match_reason"))




class ConnectionMatchingResponseAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ConnectionMatchingResponse")
        self._properties: typing.Set[str] = set([  "matches",  ])
        self._props = ConnectionMatchingResponseProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ConnectionMatchingResponseProperties":
        return self._props


class ConnectionMatchingResponseViewer(ConnectionMatchingResponseAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ConnectionMatchingResponseProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def matches(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("matches"))




class ConnectionSplittingResponseAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ConnectionSplittingResponse")
        self._properties: typing.Set[str] = set([  "incoming_connections",  "outgoing_connections",  "summary",  ])
        self._props = ConnectionSplittingResponseProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ConnectionSplittingResponseProperties":
        return self._props


class ConnectionSplittingResponseViewer(ConnectionSplittingResponseAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ConnectionSplittingResponseProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def incoming_connections(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("incoming_connections"))

    @property
    def outgoing_connections(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("outgoing_connections"))

    @property
    def summary(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("summary"))




class ContractAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("Contract")
        self._properties: typing.Set[str] = set([  "contract_id",  "contract_type",  "name",  "description",  "role",  "interface",  "input_format",  "output_format",  "error_codes",  "authentication_required",  "examples",  "instructions",  ])
        self._props = ContractProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ContractProperties":
        return self._props


class ContractViewer(ContractAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ContractProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def contract_id(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("contract_id"))

    @property
    def contract_type(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("contract_type"))

    @property
    def name(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("name"))

    @property
    def description(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("description"))

    @property
    def role(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("role"))

    @property
    def interface(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("interface"))

    @property
    def input_format(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("input_format"))

    @property
    def output_format(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("output_format"))

    @property
    def error_codes(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("error_codes"))

    @property
    def authentication_required(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("authentication_required"))

    @property
    def examples(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("examples"))

    @property
    def instructions(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("instructions"))




class ContractFieldAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ContractField")
        self._properties: typing.Set[str] = set([  "name",  "type",  "required",  "description",  "validation",  "nested",  ])
        self._props = ContractFieldProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ContractFieldProperties":
        return self._props


class ContractFieldViewer(ContractFieldAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ContractFieldProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def name(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("name"))

    @property
    def type(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("type"))

    @property
    def required(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("required"))

    @property
    def description(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("description"))

    @property
    def validation(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("validation"))

    @property
    def nested(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("nested"))




class CrossIndexingResponseAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CrossIndexingResponse")
        self._properties: typing.Set[str] = set([  "thinking",  "tool_call",  "sutra_memory",  ])
        self._props = CrossIndexingResponseProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CrossIndexingResponseProperties":
        return self._props


class CrossIndexingResponseViewer(CrossIndexingResponseAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CrossIndexingResponseProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def thinking(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("thinking"))

    @property
    def tool_call(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("tool_call"))

    @property
    def sutra_memory(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("sutra_memory"))




class DatabaseParamsAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("DatabaseParams")
        self._properties: typing.Set[str] = set([  "query_name",  "file_path",  "start_line",  "end_line",  "block_id",  "fetch_next_chunk",  ])
        self._props = DatabaseParamsProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "DatabaseParamsProperties":
        return self._props


class DatabaseParamsViewer(DatabaseParamsAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class DatabaseParamsProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def query_name(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("query_name"))

    @property
    def file_path(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("file_path"))

    @property
    def start_line(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("start_line"))

    @property
    def end_line(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("end_line"))

    @property
    def block_id(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("block_id"))

    @property
    def fetch_next_chunk(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("fetch_next_chunk"))




class DatabaseToolCallAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("DatabaseToolCall")
        self._properties: typing.Set[str] = set([  "tool_name",  "parameters",  ])
        self._props = DatabaseToolCallProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "DatabaseToolCallProperties":
        return self._props


class DatabaseToolCallViewer(DatabaseToolCallAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class DatabaseToolCallProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def tool_name(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("tool_name"))

    @property
    def parameters(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("parameters"))




class FileChangeAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("FileChange")
        self._properties: typing.Set[str] = set([  "file_path",  "operation",  "instructions",  ])
        self._props = FileChangeProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "FileChangeProperties":
        return self._props


class FileChangeViewer(FileChangeAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class FileChangeProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def file_path(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("file_path"))

    @property
    def operation(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("operation"))

    @property
    def instructions(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("instructions"))




class FileChangesAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("FileChanges")
        self._properties: typing.Set[str] = set([  "modified",  "added",  "deleted",  ])
        self._props = FileChangesProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "FileChangesProperties":
        return self._props


class FileChangesViewer(FileChangesAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class FileChangesProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def modified(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("modified"))

    @property
    def added(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("added"))

    @property
    def deleted(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("deleted"))




class ListFilesParamsAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ListFilesParams")
        self._properties: typing.Set[str] = set([  "path",  "project_name",  "recursive",  "fetch_next_chunk",  ])
        self._props = ListFilesParamsProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ListFilesParamsProperties":
        return self._props


class ListFilesParamsViewer(ListFilesParamsAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ListFilesParamsProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def path(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("path"))

    @property
    def project_name(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("project_name"))

    @property
    def recursive(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("recursive"))

    @property
    def fetch_next_chunk(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("fetch_next_chunk"))




class ListFilesParamsWithoutProjectNameAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ListFilesParamsWithoutProjectName")
        self._properties: typing.Set[str] = set([  "path",  "recursive",  "fetch_next_chunk",  ])
        self._props = ListFilesParamsWithoutProjectNameProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ListFilesParamsWithoutProjectNameProperties":
        return self._props


class ListFilesParamsWithoutProjectNameViewer(ListFilesParamsWithoutProjectNameAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ListFilesParamsWithoutProjectNameProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def path(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("path"))

    @property
    def recursive(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("recursive"))

    @property
    def fetch_next_chunk(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("fetch_next_chunk"))




class ListFilesToolCallAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ListFilesToolCall")
        self._properties: typing.Set[str] = set([  "tool_name",  "parameters",  ])
        self._props = ListFilesToolCallProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ListFilesToolCallProperties":
        return self._props


class ListFilesToolCallViewer(ListFilesToolCallAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ListFilesToolCallProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def tool_name(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("tool_name"))

    @property
    def parameters(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("parameters"))




class ListFilesToolCallWithoutProjectNameAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ListFilesToolCallWithoutProjectName")
        self._properties: typing.Set[str] = set([  "tool_name",  "parameters",  ])
        self._props = ListFilesToolCallWithoutProjectNameProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ListFilesToolCallWithoutProjectNameProperties":
        return self._props


class ListFilesToolCallWithoutProjectNameViewer(ListFilesToolCallWithoutProjectNameAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ListFilesToolCallWithoutProjectNameProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def tool_name(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("tool_name"))

    @property
    def parameters(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("parameters"))




class ProjectAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("Project")
        self._properties: typing.Set[str] = set([  "name",  "path",  "description",  ])
        self._props = ProjectProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ProjectProperties":
        return self._props


class ProjectViewer(ProjectAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ProjectProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def name(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("name"))

    @property
    def path(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("path"))

    @property
    def description(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("description"))




class ProjectContextAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ProjectContext")
        self._properties: typing.Set[str] = set([  "projects",  ])
        self._props = ProjectContextProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ProjectContextProperties":
        return self._props


class ProjectContextViewer(ProjectContextAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ProjectContextProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def projects(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("projects"))




class ProjectRoadmapAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ProjectRoadmap")
        self._properties: typing.Set[str] = set([  "project_name",  "project_path",  "impact_level",  "reasoning",  "implementation_plan",  "changes",  "contracts",  ])
        self._props = ProjectRoadmapProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ProjectRoadmapProperties":
        return self._props


class ProjectRoadmapViewer(ProjectRoadmapAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ProjectRoadmapProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def project_name(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("project_name"))

    @property
    def project_path(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("project_path"))

    @property
    def impact_level(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("impact_level"))

    @property
    def reasoning(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("reasoning"))

    @property
    def implementation_plan(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("implementation_plan"))

    @property
    def changes(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("changes"))

    @property
    def contracts(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("contracts"))




class RoadmapAgentParamsAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("RoadmapAgentParams")
        self._properties: typing.Set[str] = set([  "context",  "prompt_params",  ])
        self._props = RoadmapAgentParamsProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "RoadmapAgentParamsProperties":
        return self._props


class RoadmapAgentParamsViewer(RoadmapAgentParamsAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class RoadmapAgentParamsProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def context(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("context"))

    @property
    def prompt_params(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("prompt_params"))




class RoadmapCompletionParamsAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("RoadmapCompletionParams")
        self._properties: typing.Set[str] = set([  "projects",  "summary",  ])
        self._props = RoadmapCompletionParamsProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "RoadmapCompletionParamsProperties":
        return self._props


class RoadmapCompletionParamsViewer(RoadmapCompletionParamsAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class RoadmapCompletionParamsProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def projects(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("projects"))

    @property
    def summary(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("summary"))




class RoadmapCompletionToolCallAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("RoadmapCompletionToolCall")
        self._properties: typing.Set[str] = set([  "tool_name",  "parameters",  ])
        self._props = RoadmapCompletionToolCallProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "RoadmapCompletionToolCallProperties":
        return self._props


class RoadmapCompletionToolCallViewer(RoadmapCompletionToolCallAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class RoadmapCompletionToolCallProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def tool_name(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("tool_name"))

    @property
    def parameters(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("parameters"))




class RoadmapPromptParamsAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("RoadmapPromptParams")
        self._properties: typing.Set[str] = set([  "base_params",  ])
        self._props = RoadmapPromptParamsProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "RoadmapPromptParamsProperties":
        return self._props


class RoadmapPromptParamsViewer(RoadmapPromptParamsAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class RoadmapPromptParamsProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def base_params(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("base_params"))




class RoadmapResponseAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("RoadmapResponse")
        self._properties: typing.Set[str] = set([  "thinking",  "tool_call",  "sutra_memory",  ])
        self._props = RoadmapResponseProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "RoadmapResponseProperties":
        return self._props


class RoadmapResponseViewer(RoadmapResponseAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class RoadmapResponseProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def thinking(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("thinking"))

    @property
    def tool_call(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("tool_call"))

    @property
    def sutra_memory(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("sutra_memory"))




class SearchKeywordParamsAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("SearchKeywordParams")
        self._properties: typing.Set[str] = set([  "keyword",  "before_lines",  "after_lines",  "case_sensitive",  "regex",  "file_paths",  "project_name",  "fetch_next_chunk",  ])
        self._props = SearchKeywordParamsProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "SearchKeywordParamsProperties":
        return self._props


class SearchKeywordParamsViewer(SearchKeywordParamsAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class SearchKeywordParamsProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def keyword(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("keyword"))

    @property
    def before_lines(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("before_lines"))

    @property
    def after_lines(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("after_lines"))

    @property
    def case_sensitive(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("case_sensitive"))

    @property
    def regex(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("regex"))

    @property
    def file_paths(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("file_paths"))

    @property
    def project_name(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("project_name"))

    @property
    def fetch_next_chunk(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("fetch_next_chunk"))




class SearchKeywordParamsWithoutProjectNameAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("SearchKeywordParamsWithoutProjectName")
        self._properties: typing.Set[str] = set([  "keyword",  "file_paths",  "before_lines",  "after_lines",  "case_sensitive",  "regex",  "fetch_next_chunk",  ])
        self._props = SearchKeywordParamsWithoutProjectNameProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "SearchKeywordParamsWithoutProjectNameProperties":
        return self._props


class SearchKeywordParamsWithoutProjectNameViewer(SearchKeywordParamsWithoutProjectNameAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class SearchKeywordParamsWithoutProjectNameProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def keyword(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("keyword"))

    @property
    def file_paths(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("file_paths"))

    @property
    def before_lines(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("before_lines"))

    @property
    def after_lines(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("after_lines"))

    @property
    def case_sensitive(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("case_sensitive"))

    @property
    def regex(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("regex"))

    @property
    def fetch_next_chunk(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("fetch_next_chunk"))




class SearchKeywordToolCallAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("SearchKeywordToolCall")
        self._properties: typing.Set[str] = set([  "tool_name",  "parameters",  ])
        self._props = SearchKeywordToolCallProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "SearchKeywordToolCallProperties":
        return self._props


class SearchKeywordToolCallViewer(SearchKeywordToolCallAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class SearchKeywordToolCallProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def tool_name(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("tool_name"))

    @property
    def parameters(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("parameters"))




class SearchKeywordToolCallWithoutProjectNameAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("SearchKeywordToolCallWithoutProjectName")
        self._properties: typing.Set[str] = set([  "tool_name",  "parameters",  ])
        self._props = SearchKeywordToolCallWithoutProjectNameProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "SearchKeywordToolCallWithoutProjectNameProperties":
        return self._props


class SearchKeywordToolCallWithoutProjectNameViewer(SearchKeywordToolCallWithoutProjectNameAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class SearchKeywordToolCallWithoutProjectNameProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def tool_name(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("tool_name"))

    @property
    def parameters(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("parameters"))




class SemanticSearchParamsAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("SemanticSearchParams")
        self._properties: typing.Set[str] = set([  "query",  "project_name",  "fetch_next_chunk",  ])
        self._props = SemanticSearchParamsProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "SemanticSearchParamsProperties":
        return self._props


class SemanticSearchParamsViewer(SemanticSearchParamsAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class SemanticSearchParamsProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def query(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("query"))

    @property
    def project_name(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("project_name"))

    @property
    def fetch_next_chunk(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("fetch_next_chunk"))




class SemanticSearchToolCallAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("SemanticSearchToolCall")
        self._properties: typing.Set[str] = set([  "tool_name",  "parameters",  ])
        self._props = SemanticSearchToolCallProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "SemanticSearchToolCallProperties":
        return self._props


class SemanticSearchToolCallViewer(SemanticSearchToolCallAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class SemanticSearchToolCallProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def tool_name(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("tool_name"))

    @property
    def parameters(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("parameters"))




class SutraMemoryParamsAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("SutraMemoryParams")
        self._properties: typing.Set[str] = set([  "add_history",  "tasks",  "code",  "files",  ])
        self._props = SutraMemoryParamsProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "SutraMemoryParamsProperties":
        return self._props


class SutraMemoryParamsViewer(SutraMemoryParamsAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class SutraMemoryParamsProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def add_history(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("add_history"))

    @property
    def tasks(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("tasks"))

    @property
    def code(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("code"))

    @property
    def files(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("files"))




class SutraMemoryParams_CrossIndexingAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("SutraMemoryParams_CrossIndexing")
        self._properties: typing.Set[str] = set([  "add_history",  "tasks",  "code",  ])
        self._props = SutraMemoryParams_CrossIndexingProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "SutraMemoryParams_CrossIndexingProperties":
        return self._props


class SutraMemoryParams_CrossIndexingViewer(SutraMemoryParams_CrossIndexingAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class SutraMemoryParams_CrossIndexingProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def add_history(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("add_history"))

    @property
    def tasks(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("tasks"))

    @property
    def code(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("code"))




class SystemInfoParamsAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("SystemInfoParams")
        self._properties: typing.Set[str] = set([  "os",  "shell",  "home",  "current_dir",  ])
        self._props = SystemInfoParamsProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "SystemInfoParamsProperties":
        return self._props


class SystemInfoParamsViewer(SystemInfoParamsAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class SystemInfoParamsProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def os(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("os"))

    @property
    def shell(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("shell"))

    @property
    def home(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("home"))

    @property
    def current_dir(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("current_dir"))




class SystemInfo_CrossIndexingAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("SystemInfo_CrossIndexing")
        self._properties: typing.Set[str] = set([  "home",  "current_dir",  ])
        self._props = SystemInfo_CrossIndexingProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "SystemInfo_CrossIndexingProperties":
        return self._props


class SystemInfo_CrossIndexingViewer(SystemInfo_CrossIndexingAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class SystemInfo_CrossIndexingProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def home(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("home"))

    @property
    def current_dir(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("current_dir"))




class TaskFilterResponseAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("TaskFilterResponse")
        self._properties: typing.Set[str] = set([  "tasks",  ])
        self._props = TaskFilterResponseProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "TaskFilterResponseProperties":
        return self._props


class TaskFilterResponseViewer(TaskFilterResponseAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class TaskFilterResponseProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def tasks(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("tasks"))




class TaskOperationAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("TaskOperation")
        self._properties: typing.Set[str] = set([  "action",  "id",  "from_status",  "to_status",  "description",  ])
        self._props = TaskOperationProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "TaskOperationProperties":
        return self._props


class TaskOperationViewer(TaskOperationAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class TaskOperationProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def action(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("action"))

    @property
    def id(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("id"))

    @property
    def from_status(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("from_status"))

    @property
    def to_status(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("to_status"))

    @property
    def description(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("description"))




class TaskOperation_CrossIndexingAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("TaskOperation_CrossIndexing")
        self._properties: typing.Set[str] = set([  "action",  "id",  "from_status",  "to_status",  "description",  ])
        self._props = TaskOperation_CrossIndexingProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "TaskOperation_CrossIndexingProperties":
        return self._props


class TaskOperation_CrossIndexingViewer(TaskOperation_CrossIndexingAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class TaskOperation_CrossIndexingProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def action(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("action"))

    @property
    def id(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("id"))

    @property
    def from_status(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("from_status"))

    @property
    def to_status(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("to_status"))

    @property
    def description(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("description"))




class TechnologyCorrectionAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("TechnologyCorrection")
        self._properties: typing.Set[str] = set([  "original_name",  "corrected_name",  ])
        self._props = TechnologyCorrectionProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "TechnologyCorrectionProperties":
        return self._props


class TechnologyCorrectionViewer(TechnologyCorrectionAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class TechnologyCorrectionProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def original_name(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("original_name"))

    @property
    def corrected_name(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("corrected_name"))




class TechnologyCorrectionResponseAst:
    def __init__(self, tb: type_builder.TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("TechnologyCorrectionResponse")
        self._properties: typing.Set[str] = set([  "corrections",  ])
        self._props = TechnologyCorrectionResponseProperties(self._bldr, self._properties)

    def type(self) -> baml_py.FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "TechnologyCorrectionResponseProperties":
        return self._props


class TechnologyCorrectionResponseViewer(TechnologyCorrectionResponseAst):
    def __init__(self, tb: type_builder.TypeBuilder):
        super().__init__(tb)


    def list_properties(self) -> typing.List[typing.Tuple[str, type_builder.ClassPropertyViewer]]:
        return [(name, type_builder.ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class TechnologyCorrectionResponseProperties:
    def __init__(self, bldr: baml_py.ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties # type: ignore (we know how to use this private attribute) # noqa: F821



    @property
    def corrections(self) -> type_builder.ClassPropertyViewer:
        return type_builder.ClassPropertyViewer(self.__bldr.property("corrections"))

# ----------------------------------------------------------------------------
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml
#
# ----------------------------------------------------------------------------

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code using: baml-cli generate
# baml-cli is available with the baml package.

import typing
import typing_extensions
from pydantic import BaseModel, ConfigDict

import baml_py

from . import types

StreamStateValueT = typing.TypeVar('StreamStateValueT')
class StreamState(BaseModel, typing.Generic[StreamStateValueT]):
    value: StreamStateValueT
    state: typing_extensions.Literal["Pending", "Incomplete", "Complete"]
# #########################################################################
# Generated classes (49)
# #########################################################################

class AddTask(BaseModel):
    id: typing.Optional[int] = None
    description: typing.Optional[str] = None

class BaseCompletionParams(BaseModel):
    result: typing.Optional[str] = None

class BasePromptParams(BaseModel):
    system_info: typing.Optional["SystemInfoParams"] = None
    project_context: typing.Optional["ProjectContext"] = None

class ChangeInstruction(BaseModel):
    description: typing.Optional[str] = None
    current_state: typing.Optional[str] = None
    target_state: typing.Optional[str] = None
    start_line: typing.Optional[int] = None
    end_line: typing.Optional[int] = None
    additional_notes: typing.Optional[str] = None

class CodeConnection(BaseModel):
    id: typing.Optional[str] = None
    file: typing.Optional[str] = None
    start_line: typing.Optional[int] = None
    end_line: typing.Optional[int] = None
    description: typing.Optional[str] = None

class CodeManagerResponse(BaseModel):
    thinking: typing.Optional[str] = None
    connection_code: typing.Optional[typing.List["CodeConnection"]] = None

class CodeStorage(BaseModel):
    action: typing.Optional[types.CodeStorageAction] = None
    id: typing.Optional[str] = None
    file: typing.Optional[str] = None
    start_line: typing.Optional[int] = None
    end_line: typing.Optional[int] = None
    description: typing.Optional[str] = None

class CodeStorage_CrossIndexing(BaseModel):
    action: typing.Optional[types.CodeStorageAction_CrossIndexing] = None
    id: typing.Optional[str] = None
    file: typing.Optional[str] = None
    start_line: typing.Optional[int] = None
    end_line: typing.Optional[int] = None
    description: typing.Optional[str] = None

class CompletionResponse_CrossIndexing(BaseModel):
    result: typing.Optional[str] = None

class CompletionToolCall(BaseModel):
    tool_name: typing.Optional[str] = None
    parameters: typing.Optional["BaseCompletionParams"] = None

class CompletionToolCall_CrossIndexing(BaseModel):
    tool_name: typing.Optional[str] = None
    parameters: typing.Optional["CompletionResponse_CrossIndexing"] = None

class ConnectionDetail(BaseModel):
    snippet_lines: typing.Optional[str] = None
    description: typing.Optional[str] = None

class ConnectionMatch(BaseModel):
    incoming_id: typing.Optional[str] = None
    outgoing_id: typing.Optional[str] = None
    match_confidence: typing.Optional[str] = None
    match_reason: typing.Optional[str] = None

class ConnectionMatchingResponse(BaseModel):
    matches: typing.Optional[typing.List["ConnectionMatch"]] = None

class ConnectionSplittingResponse(BaseModel):
    incoming_connections: typing.Optional[typing.Dict[typing.Union[types.TechnologyType, str], typing.Dict[str, typing.List["ConnectionDetail"]]]] = None
    outgoing_connections: typing.Optional[typing.Dict[typing.Union[types.TechnologyType, str], typing.Dict[str, typing.List["ConnectionDetail"]]]] = None
    summary: typing.Optional[str] = None

class Contract(BaseModel):
    contract_id: typing.Optional[str] = None
    contract_type: typing.Optional[str] = None
    name: typing.Optional[str] = None
    description: typing.Optional[str] = None
    role: typing.Optional[types.ContractRole] = None
    interface: typing.Dict[str, str]
    input_format: typing.Optional[typing.List["ContractField"]] = None
    output_format: typing.Optional[typing.List["ContractField"]] = None
    error_codes: typing.Optional[typing.List[str]] = None
    authentication_required: typing.Optional[bool] = None
    examples: typing.Optional[str] = None
    instructions: typing.Optional[str] = None

class ContractField(BaseModel):
    name: typing.Optional[str] = None
    type: typing.Optional[str] = None
    required: typing.Optional[bool] = None
    description: typing.Optional[str] = None
    validation: typing.Optional[str] = None
    nested: typing.Optional[typing.List["ContractField"]] = None

class CrossIndexingResponse(BaseModel):
    thinking: typing.Optional[str] = None
    tool_call: typing.Optional[typing.Union["ListFilesToolCallWithoutProjectName", "DatabaseToolCall", "SearchKeywordToolCallWithoutProjectName", "CompletionToolCall_CrossIndexing"]] = None
    sutra_memory: typing.Optional["SutraMemoryParams_CrossIndexing"] = None

class DatabaseParams(BaseModel):
    query_name: typing.Optional[str] = None
    file_path: typing.Optional[str] = None
    start_line: typing.Optional[int] = None
    end_line: typing.Optional[int] = None
    block_id: typing.Optional[str] = None
    fetch_next_chunk: typing.Optional[bool] = None

class DatabaseToolCall(BaseModel):
    tool_name: typing.Optional[str] = None
    parameters: typing.Optional["DatabaseParams"] = None

class FileChange(BaseModel):
    file_path: typing.Optional[str] = None
    operation: typing.Optional[types.FileOperation] = None
    instructions: typing.List["ChangeInstruction"]

class FileChanges(BaseModel):
    modified: typing.Optional[typing.List[str]] = None
    added: typing.Optional[typing.List[str]] = None
    deleted: typing.Optional[typing.List[str]] = None

class ListFilesParams(BaseModel):
    path: typing.Optional[str] = None
    project_name: typing.Optional[str] = None
    recursive: typing.Optional[bool] = None
    fetch_next_chunk: typing.Optional[bool] = None

class ListFilesParamsWithoutProjectName(BaseModel):
    path: typing.Optional[str] = None
    recursive: typing.Optional[bool] = None
    fetch_next_chunk: typing.Optional[bool] = None

class ListFilesToolCall(BaseModel):
    tool_name: typing.Optional[str] = None
    parameters: typing.Optional["ListFilesParams"] = None

class ListFilesToolCallWithoutProjectName(BaseModel):
    tool_name: typing.Optional[str] = None
    parameters: typing.Optional["ListFilesParamsWithoutProjectName"] = None

class Project(BaseModel):
    name: typing.Optional[str] = None
    path: typing.Optional[str] = None
    description: typing.Optional[str] = None

class ProjectContext(BaseModel):
    projects: typing.List["Project"]

class ProjectRoadmap(BaseModel):
    project_name: typing.Optional[str] = None
    project_path: typing.Optional[str] = None
    impact_level: typing.Optional[types.ImpactLevel] = None
    reasoning: typing.Optional[str] = None
    implementation_plan: typing.List[str]
    changes: typing.Optional[typing.List["FileChange"]] = None
    contracts: typing.Optional[typing.List["Contract"]] = None

class RoadmapAgentParams(BaseModel):
    context: typing.Optional[str] = None
    prompt_params: typing.Optional["RoadmapPromptParams"] = None

class RoadmapCompletionParams(BaseModel):
    projects: typing.List["ProjectRoadmap"]
    summary: typing.Optional[str] = None

class RoadmapCompletionToolCall(BaseModel):
    tool_name: typing.Optional[str] = None
    parameters: typing.Optional[typing.Union["RoadmapCompletionParams", "BaseCompletionParams"]] = None

class RoadmapPromptParams(BaseModel):
    base_params: typing.Optional["BasePromptParams"] = None

class RoadmapResponse(BaseModel):
    thinking: typing.Optional[str] = None
    tool_call: typing.Optional[typing.Union["DatabaseToolCall", "SearchKeywordToolCall", "SemanticSearchToolCall", "ListFilesToolCall", "RoadmapCompletionToolCall"]] = None
    sutra_memory: typing.Optional["SutraMemoryParams"] = None

class SearchKeywordParams(BaseModel):
    keyword: typing.Optional[str] = None
    before_lines: typing.Optional[int] = None
    after_lines: typing.Optional[int] = None
    case_sensitive: typing.Optional[bool] = None
    regex: typing.Optional[bool] = None
    file_paths: typing.Optional[str] = None
    project_name: typing.Optional[str] = None
    fetch_next_chunk: typing.Optional[bool] = None

class SearchKeywordParamsWithoutProjectName(BaseModel):
    keyword: typing.Optional[str] = None
    file_paths: typing.Optional[str] = None
    before_lines: typing.Optional[int] = None
    after_lines: typing.Optional[int] = None
    case_sensitive: typing.Optional[bool] = None
    regex: typing.Optional[bool] = None
    fetch_next_chunk: typing.Optional[bool] = None

class SearchKeywordToolCall(BaseModel):
    tool_name: typing.Optional[str] = None
    parameters: typing.Optional["SearchKeywordParams"] = None

class SearchKeywordToolCallWithoutProjectName(BaseModel):
    tool_name: typing.Optional[str] = None
    parameters: typing.Optional["SearchKeywordParamsWithoutProjectName"] = None

class SemanticSearchParams(BaseModel):
    query: typing.Optional[str] = None
    project_name: typing.Optional[str] = None
    fetch_next_chunk: typing.Optional[bool] = None

class SemanticSearchToolCall(BaseModel):
    tool_name: typing.Optional[str] = None
    parameters: typing.Optional["SemanticSearchParams"] = None

class SutraMemoryParams(BaseModel):
    add_history: typing.Optional[str] = None
    tasks: typing.Optional[typing.List["TaskOperation"]] = None
    code: typing.Optional[typing.List["CodeStorage"]] = None
    files: typing.Optional["FileChanges"] = None

class SutraMemoryParams_CrossIndexing(BaseModel):
    add_history: typing.Optional[str] = None
    tasks: typing.Optional[typing.List["TaskOperation_CrossIndexing"]] = None
    code: typing.Optional[typing.List["CodeStorage_CrossIndexing"]] = None

class SystemInfoParams(BaseModel):
    os: typing.Optional[str] = None
    shell: typing.Optional[str] = None
    home: typing.Optional[str] = None
    current_dir: typing.Optional[str] = None

class SystemInfo_CrossIndexing(BaseModel):
    home: typing.Optional[str] = None
    current_dir: typing.Optional[str] = None

class TaskFilterResponse(BaseModel):
    tasks: typing.List["AddTask"]

class TaskOperation(BaseModel):
    action: typing.Optional[types.TaskOperationAction] = None
    id: typing.Optional[str] = None
    from_status: typing.Optional[types.TaskStatus] = None
    to_status: typing.Optional[types.TaskStatus] = None
    description: typing.Optional[str] = None

class TaskOperation_CrossIndexing(BaseModel):
    action: typing.Optional[types.TaskOperationAction_CrossIndexing] = None
    id: typing.Optional[str] = None
    from_status: typing.Optional[types.Status_CrossIndexing] = None
    to_status: typing.Optional[types.Status_CrossIndexing] = None
    description: typing.Optional[str] = None

class TechnologyCorrection(BaseModel):
    original_name: typing.Optional[str] = None
    corrected_name: typing.Optional[str] = None

class TechnologyCorrectionResponse(BaseModel):
    corrections: typing.Optional[typing.List["TechnologyCorrection"]] = None

# #########################################################################
# Generated type aliases (2)
# #########################################################################


RoadmapToolCall: typing_extensions.TypeAlias = typing.Optional[typing.Union["DatabaseToolCall", "SearchKeywordToolCall", "SemanticSearchToolCall", "ListFilesToolCall", "RoadmapCompletionToolCall"]]


ToolCall_CrossIndexing: typing_extensions.TypeAlias = typing.Optional[typing.Union["ListFilesToolCallWithoutProjectName", "DatabaseToolCall", "SearchKeywordToolCallWithoutProjectName", "CompletionToolCall_CrossIndexing"]]

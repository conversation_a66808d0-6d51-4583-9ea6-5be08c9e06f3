# ----------------------------------------------------------------------------
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml
#
# ----------------------------------------------------------------------------

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code using: baml-cli generate
# baml-cli is available with the baml package.

import typing
import typing_extensions
import baml_py

from . import stream_types, types, type_builder
from .parser import LlmResponseParser, LlmStreamParser
from .runtime import DoNotUseDirectlyCallManager, BamlCallOptions
from .globals import DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME as __runtime__

class BamlSyncClient:
    __options: DoNotUseDirectlyCallManager
    __stream_client: "BamlStreamClient"
    __http_request: "BamlHttpRequestClient"
    __http_stream_request: "BamlHttpStreamRequestClient"
    __llm_response_parser: LlmResponseParser
    __llm_stream_parser: LlmStreamParser

    def __init__(self, options: DoNotUseDirectlyCallManager):
        self.__options = options
        self.__stream_client = BamlStreamClient(options)
        self.__http_request = BamlHttpRequestClient(options)
        self.__http_stream_request = BamlHttpStreamRequestClient(options)
        self.__llm_response_parser = LlmResponseParser(options)
        self.__llm_stream_parser = LlmStreamParser(options)

    def __getstate__(self):
        # Return state needed for pickling
        return {"options": self.__options}

    def __setstate__(self, state):
        # Restore state from pickling
        self.__options = state["options"]
        self.__stream_client = BamlStreamClient(self.__options)
        self.__http_request = BamlHttpRequestClient(self.__options)
        self.__http_stream_request = BamlHttpStreamRequestClient(self.__options)
        self.__llm_response_parser = LlmResponseParser(self.__options)
        self.__llm_stream_parser = LlmStreamParser(self.__options)

    def with_options(self,
        tb: typing.Optional[type_builder.TypeBuilder] = None,
        client_registry: typing.Optional[baml_py.baml_py.ClientRegistry] = None,
        collector: typing.Optional[typing.Union[baml_py.baml_py.Collector, typing.List[baml_py.baml_py.Collector]]] = None,
        env: typing.Optional[typing.Dict[str, typing.Optional[str]]] = None,
        on_tick: typing.Optional[typing.Callable[[str, baml_py.baml_py.FunctionLog], None]] = None,
    ) -> "BamlSyncClient":
        options: BamlCallOptions = {}
        if tb is not None:
            options["tb"] = tb
        if client_registry is not None:
            options["client_registry"] = client_registry
        if collector is not None:
            options["collector"] = collector
        if env is not None:
            options["env"] = env
        if on_tick is not None:
            options["on_tick"] = on_tick
        return BamlSyncClient(self.__options.merge_options(options))

    @property
    def stream(self):
      return self.__stream_client

    @property
    def request(self):
      return self.__http_request

    @property
    def stream_request(self):
      return self.__http_stream_request

    @property
    def parse(self):
      return self.__llm_response_parser

    @property
    def parse_stream(self):
      return self.__llm_stream_parser

    def AnthropicCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CodeManagerResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.AnthropicCodeManager(tool_results=tool_results,system_info=system_info,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="AnthropicCodeManager", args={
                "tool_results": tool_results,"system_info": system_info,
            })
            return typing.cast(types.CodeManagerResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def AnthropicConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> types.ConnectionMatchingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.AnthropicConnectionMatching(incoming_connections=incoming_connections,outgoing_connections=outgoing_connections,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="AnthropicConnectionMatching", args={
                "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
            })
            return typing.cast(types.ConnectionMatchingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def AnthropicConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> types.ConnectionSplittingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.AnthropicConnectionSplitting(memory_context=memory_context,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="AnthropicConnectionSplitting", args={
                "memory_context": memory_context,
            })
            return typing.cast(types.ConnectionSplittingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def AnthropicImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.AnthropicImplementationDiscovery(analysis_query=analysis_query,memory_context=memory_context,system_info=system_info,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="AnthropicImplementationDiscovery", args={
                "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
            })
            return typing.cast(types.CrossIndexingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def AnthropicImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.AnthropicImportDiscovery(analysis_query=analysis_query,memory_context=memory_context,system_info=system_info,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="AnthropicImportDiscovery", args={
                "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
            })
            return typing.cast(types.CrossIndexingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def AnthropicPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.AnthropicPackageDiscovery(analysis_query=analysis_query,memory_context=memory_context,system_info=system_info,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="AnthropicPackageDiscovery", args={
                "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
            })
            return typing.cast(types.CrossIndexingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def AnthropicRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> types.RoadmapResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.AnthropicRoadmapAgent(params=params,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="AnthropicRoadmapAgent", args={
                "params": params,
            })
            return typing.cast(types.RoadmapResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def AnthropicTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> types.TaskFilterResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.AnthropicTaskFilter(task_list=task_list,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="AnthropicTaskFilter", args={
                "task_list": task_list,
            })
            return typing.cast(types.TaskFilterResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def AnthropicTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> types.TechnologyCorrectionResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.AnthropicTechnologyCorrection(unmatched_names=unmatched_names,acceptable_enums=acceptable_enums,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="AnthropicTechnologyCorrection", args={
                "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
            })
            return typing.cast(types.TechnologyCorrectionResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def AwsCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CodeManagerResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.AwsCodeManager(tool_results=tool_results,system_info=system_info,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="AwsCodeManager", args={
                "tool_results": tool_results,"system_info": system_info,
            })
            return typing.cast(types.CodeManagerResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def AwsConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> types.ConnectionMatchingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.AwsConnectionMatching(incoming_connections=incoming_connections,outgoing_connections=outgoing_connections,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="AwsConnectionMatching", args={
                "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
            })
            return typing.cast(types.ConnectionMatchingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def AwsConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> types.ConnectionSplittingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.AwsConnectionSplitting(memory_context=memory_context,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="AwsConnectionSplitting", args={
                "memory_context": memory_context,
            })
            return typing.cast(types.ConnectionSplittingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def AwsImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.AwsImplementationDiscovery(analysis_query=analysis_query,memory_context=memory_context,system_info=system_info,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="AwsImplementationDiscovery", args={
                "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
            })
            return typing.cast(types.CrossIndexingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def AwsImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.AwsImportDiscovery(analysis_query=analysis_query,memory_context=memory_context,system_info=system_info,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="AwsImportDiscovery", args={
                "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
            })
            return typing.cast(types.CrossIndexingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def AwsPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.AwsPackageDiscovery(analysis_query=analysis_query,memory_context=memory_context,system_info=system_info,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="AwsPackageDiscovery", args={
                "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
            })
            return typing.cast(types.CrossIndexingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def AwsRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> types.RoadmapResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.AwsRoadmapAgent(params=params,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="AwsRoadmapAgent", args={
                "params": params,
            })
            return typing.cast(types.RoadmapResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def AwsTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> types.TaskFilterResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.AwsTaskFilter(task_list=task_list,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="AwsTaskFilter", args={
                "task_list": task_list,
            })
            return typing.cast(types.TaskFilterResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def AwsTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> types.TechnologyCorrectionResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.AwsTechnologyCorrection(unmatched_names=unmatched_names,acceptable_enums=acceptable_enums,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="AwsTechnologyCorrection", args={
                "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
            })
            return typing.cast(types.TechnologyCorrectionResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def ChatGPTCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CodeManagerResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.ChatGPTCodeManager(tool_results=tool_results,system_info=system_info,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="ChatGPTCodeManager", args={
                "tool_results": tool_results,"system_info": system_info,
            })
            return typing.cast(types.CodeManagerResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def ChatGPTConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> types.ConnectionMatchingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.ChatGPTConnectionMatching(incoming_connections=incoming_connections,outgoing_connections=outgoing_connections,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="ChatGPTConnectionMatching", args={
                "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
            })
            return typing.cast(types.ConnectionMatchingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def ChatGPTConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> types.ConnectionSplittingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.ChatGPTConnectionSplitting(memory_context=memory_context,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="ChatGPTConnectionSplitting", args={
                "memory_context": memory_context,
            })
            return typing.cast(types.ConnectionSplittingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def ChatGPTImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.ChatGPTImplementationDiscovery(analysis_query=analysis_query,memory_context=memory_context,system_info=system_info,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="ChatGPTImplementationDiscovery", args={
                "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
            })
            return typing.cast(types.CrossIndexingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def ChatGPTImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.ChatGPTImportDiscovery(analysis_query=analysis_query,memory_context=memory_context,system_info=system_info,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="ChatGPTImportDiscovery", args={
                "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
            })
            return typing.cast(types.CrossIndexingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def ChatGPTPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.ChatGPTPackageDiscovery(analysis_query=analysis_query,memory_context=memory_context,system_info=system_info,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="ChatGPTPackageDiscovery", args={
                "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
            })
            return typing.cast(types.CrossIndexingResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def ChatGPTRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> types.RoadmapResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.ChatGPTRoadmapAgent(params=params,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="ChatGPTRoadmapAgent", args={
                "params": params,
            })
            return typing.cast(types.RoadmapResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def ChatGPTTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> types.TaskFilterResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.ChatGPTTaskFilter(task_list=task_list,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="ChatGPTTaskFilter", args={
                "task_list": task_list,
            })
            return typing.cast(types.TaskFilterResponse, result.cast_to(types, types, stream_types, False, __runtime__))
    def ChatGPTTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> types.TechnologyCorrectionResponse:
        # Check if on_tick is provided
        if 'on_tick' in baml_options:
            stream = self.stream.ChatGPTTechnologyCorrection(unmatched_names=unmatched_names,acceptable_enums=acceptable_enums,
                baml_options=baml_options)
            return stream.get_final_response()
        else:
            # Original non-streaming code
            result = self.__options.merge_options(baml_options).call_function_sync(function_name="ChatGPTTechnologyCorrection", args={
                "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
            })
            return typing.cast(types.TechnologyCorrectionResponse, result.cast_to(types, types, stream_types, False, __runtime__))



class BamlStreamClient:
    __options: DoNotUseDirectlyCallManager

    def __init__(self, options: DoNotUseDirectlyCallManager):
        self.__options = options

    def AnthropicCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.CodeManagerResponse, types.CodeManagerResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="AnthropicCodeManager", args={
            "tool_results": tool_results,"system_info": system_info,
        })
        return baml_py.BamlSyncStream[stream_types.CodeManagerResponse, types.CodeManagerResponse](
          result,
          lambda x: typing.cast(stream_types.CodeManagerResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CodeManagerResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AnthropicConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.ConnectionMatchingResponse, types.ConnectionMatchingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="AnthropicConnectionMatching", args={
            "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
        })
        return baml_py.BamlSyncStream[stream_types.ConnectionMatchingResponse, types.ConnectionMatchingResponse](
          result,
          lambda x: typing.cast(stream_types.ConnectionMatchingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.ConnectionMatchingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AnthropicConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.ConnectionSplittingResponse, types.ConnectionSplittingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="AnthropicConnectionSplitting", args={
            "memory_context": memory_context,
        })
        return baml_py.BamlSyncStream[stream_types.ConnectionSplittingResponse, types.ConnectionSplittingResponse](
          result,
          lambda x: typing.cast(stream_types.ConnectionSplittingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.ConnectionSplittingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AnthropicImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="AnthropicImplementationDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        })
        return baml_py.BamlSyncStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse](
          result,
          lambda x: typing.cast(stream_types.CrossIndexingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CrossIndexingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AnthropicImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="AnthropicImportDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        })
        return baml_py.BamlSyncStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse](
          result,
          lambda x: typing.cast(stream_types.CrossIndexingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CrossIndexingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AnthropicPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="AnthropicPackageDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        })
        return baml_py.BamlSyncStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse](
          result,
          lambda x: typing.cast(stream_types.CrossIndexingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CrossIndexingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AnthropicRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.RoadmapResponse, types.RoadmapResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="AnthropicRoadmapAgent", args={
            "params": params,
        })
        return baml_py.BamlSyncStream[stream_types.RoadmapResponse, types.RoadmapResponse](
          result,
          lambda x: typing.cast(stream_types.RoadmapResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.RoadmapResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AnthropicTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.TaskFilterResponse, types.TaskFilterResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="AnthropicTaskFilter", args={
            "task_list": task_list,
        })
        return baml_py.BamlSyncStream[stream_types.TaskFilterResponse, types.TaskFilterResponse](
          result,
          lambda x: typing.cast(stream_types.TaskFilterResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.TaskFilterResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AnthropicTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.TechnologyCorrectionResponse, types.TechnologyCorrectionResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="AnthropicTechnologyCorrection", args={
            "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
        })
        return baml_py.BamlSyncStream[stream_types.TechnologyCorrectionResponse, types.TechnologyCorrectionResponse](
          result,
          lambda x: typing.cast(stream_types.TechnologyCorrectionResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.TechnologyCorrectionResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AwsCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.CodeManagerResponse, types.CodeManagerResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="AwsCodeManager", args={
            "tool_results": tool_results,"system_info": system_info,
        })
        return baml_py.BamlSyncStream[stream_types.CodeManagerResponse, types.CodeManagerResponse](
          result,
          lambda x: typing.cast(stream_types.CodeManagerResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CodeManagerResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AwsConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.ConnectionMatchingResponse, types.ConnectionMatchingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="AwsConnectionMatching", args={
            "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
        })
        return baml_py.BamlSyncStream[stream_types.ConnectionMatchingResponse, types.ConnectionMatchingResponse](
          result,
          lambda x: typing.cast(stream_types.ConnectionMatchingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.ConnectionMatchingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AwsConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.ConnectionSplittingResponse, types.ConnectionSplittingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="AwsConnectionSplitting", args={
            "memory_context": memory_context,
        })
        return baml_py.BamlSyncStream[stream_types.ConnectionSplittingResponse, types.ConnectionSplittingResponse](
          result,
          lambda x: typing.cast(stream_types.ConnectionSplittingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.ConnectionSplittingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AwsImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="AwsImplementationDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        })
        return baml_py.BamlSyncStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse](
          result,
          lambda x: typing.cast(stream_types.CrossIndexingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CrossIndexingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AwsImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="AwsImportDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        })
        return baml_py.BamlSyncStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse](
          result,
          lambda x: typing.cast(stream_types.CrossIndexingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CrossIndexingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AwsPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="AwsPackageDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        })
        return baml_py.BamlSyncStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse](
          result,
          lambda x: typing.cast(stream_types.CrossIndexingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CrossIndexingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AwsRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.RoadmapResponse, types.RoadmapResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="AwsRoadmapAgent", args={
            "params": params,
        })
        return baml_py.BamlSyncStream[stream_types.RoadmapResponse, types.RoadmapResponse](
          result,
          lambda x: typing.cast(stream_types.RoadmapResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.RoadmapResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AwsTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.TaskFilterResponse, types.TaskFilterResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="AwsTaskFilter", args={
            "task_list": task_list,
        })
        return baml_py.BamlSyncStream[stream_types.TaskFilterResponse, types.TaskFilterResponse](
          result,
          lambda x: typing.cast(stream_types.TaskFilterResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.TaskFilterResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def AwsTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.TechnologyCorrectionResponse, types.TechnologyCorrectionResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="AwsTechnologyCorrection", args={
            "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
        })
        return baml_py.BamlSyncStream[stream_types.TechnologyCorrectionResponse, types.TechnologyCorrectionResponse](
          result,
          lambda x: typing.cast(stream_types.TechnologyCorrectionResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.TechnologyCorrectionResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def ChatGPTCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.CodeManagerResponse, types.CodeManagerResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="ChatGPTCodeManager", args={
            "tool_results": tool_results,"system_info": system_info,
        })
        return baml_py.BamlSyncStream[stream_types.CodeManagerResponse, types.CodeManagerResponse](
          result,
          lambda x: typing.cast(stream_types.CodeManagerResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CodeManagerResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def ChatGPTConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.ConnectionMatchingResponse, types.ConnectionMatchingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="ChatGPTConnectionMatching", args={
            "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
        })
        return baml_py.BamlSyncStream[stream_types.ConnectionMatchingResponse, types.ConnectionMatchingResponse](
          result,
          lambda x: typing.cast(stream_types.ConnectionMatchingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.ConnectionMatchingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def ChatGPTConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.ConnectionSplittingResponse, types.ConnectionSplittingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="ChatGPTConnectionSplitting", args={
            "memory_context": memory_context,
        })
        return baml_py.BamlSyncStream[stream_types.ConnectionSplittingResponse, types.ConnectionSplittingResponse](
          result,
          lambda x: typing.cast(stream_types.ConnectionSplittingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.ConnectionSplittingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def ChatGPTImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="ChatGPTImplementationDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        })
        return baml_py.BamlSyncStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse](
          result,
          lambda x: typing.cast(stream_types.CrossIndexingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CrossIndexingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def ChatGPTImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="ChatGPTImportDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        })
        return baml_py.BamlSyncStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse](
          result,
          lambda x: typing.cast(stream_types.CrossIndexingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CrossIndexingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def ChatGPTPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="ChatGPTPackageDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        })
        return baml_py.BamlSyncStream[stream_types.CrossIndexingResponse, types.CrossIndexingResponse](
          result,
          lambda x: typing.cast(stream_types.CrossIndexingResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.CrossIndexingResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def ChatGPTRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.RoadmapResponse, types.RoadmapResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="ChatGPTRoadmapAgent", args={
            "params": params,
        })
        return baml_py.BamlSyncStream[stream_types.RoadmapResponse, types.RoadmapResponse](
          result,
          lambda x: typing.cast(stream_types.RoadmapResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.RoadmapResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def ChatGPTTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.TaskFilterResponse, types.TaskFilterResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="ChatGPTTaskFilter", args={
            "task_list": task_list,
        })
        return baml_py.BamlSyncStream[stream_types.TaskFilterResponse, types.TaskFilterResponse](
          result,
          lambda x: typing.cast(stream_types.TaskFilterResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.TaskFilterResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )
    def ChatGPTTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[stream_types.TechnologyCorrectionResponse, types.TechnologyCorrectionResponse]:
        ctx, result = self.__options.merge_options(baml_options).create_sync_stream(function_name="ChatGPTTechnologyCorrection", args={
            "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
        })
        return baml_py.BamlSyncStream[stream_types.TechnologyCorrectionResponse, types.TechnologyCorrectionResponse](
          result,
          lambda x: typing.cast(stream_types.TechnologyCorrectionResponse, x.cast_to(types, types, stream_types, True, __runtime__)),
          lambda x: typing.cast(types.TechnologyCorrectionResponse, x.cast_to(types, types, stream_types, False, __runtime__)),
          ctx,
        )


class BamlHttpRequestClient:
    __options: DoNotUseDirectlyCallManager

    def __init__(self, options: DoNotUseDirectlyCallManager):
        self.__options = options

    def AnthropicCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AnthropicCodeManager", args={
            "tool_results": tool_results,"system_info": system_info,
        }, mode="request")
        return result
    def AnthropicConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AnthropicConnectionMatching", args={
            "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
        }, mode="request")
        return result
    def AnthropicConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AnthropicConnectionSplitting", args={
            "memory_context": memory_context,
        }, mode="request")
        return result
    def AnthropicImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AnthropicImplementationDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="request")
        return result
    def AnthropicImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AnthropicImportDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="request")
        return result
    def AnthropicPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AnthropicPackageDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="request")
        return result
    def AnthropicRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AnthropicRoadmapAgent", args={
            "params": params,
        }, mode="request")
        return result
    def AnthropicTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AnthropicTaskFilter", args={
            "task_list": task_list,
        }, mode="request")
        return result
    def AnthropicTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AnthropicTechnologyCorrection", args={
            "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
        }, mode="request")
        return result
    def AwsCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AwsCodeManager", args={
            "tool_results": tool_results,"system_info": system_info,
        }, mode="request")
        return result
    def AwsConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AwsConnectionMatching", args={
            "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
        }, mode="request")
        return result
    def AwsConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AwsConnectionSplitting", args={
            "memory_context": memory_context,
        }, mode="request")
        return result
    def AwsImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AwsImplementationDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="request")
        return result
    def AwsImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AwsImportDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="request")
        return result
    def AwsPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AwsPackageDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="request")
        return result
    def AwsRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AwsRoadmapAgent", args={
            "params": params,
        }, mode="request")
        return result
    def AwsTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AwsTaskFilter", args={
            "task_list": task_list,
        }, mode="request")
        return result
    def AwsTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AwsTechnologyCorrection", args={
            "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
        }, mode="request")
        return result
    def ChatGPTCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="ChatGPTCodeManager", args={
            "tool_results": tool_results,"system_info": system_info,
        }, mode="request")
        return result
    def ChatGPTConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="ChatGPTConnectionMatching", args={
            "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
        }, mode="request")
        return result
    def ChatGPTConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="ChatGPTConnectionSplitting", args={
            "memory_context": memory_context,
        }, mode="request")
        return result
    def ChatGPTImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="ChatGPTImplementationDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="request")
        return result
    def ChatGPTImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="ChatGPTImportDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="request")
        return result
    def ChatGPTPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="ChatGPTPackageDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="request")
        return result
    def ChatGPTRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="ChatGPTRoadmapAgent", args={
            "params": params,
        }, mode="request")
        return result
    def ChatGPTTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="ChatGPTTaskFilter", args={
            "task_list": task_list,
        }, mode="request")
        return result
    def ChatGPTTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="ChatGPTTechnologyCorrection", args={
            "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
        }, mode="request")
        return result


class BamlHttpStreamRequestClient:
    __options: DoNotUseDirectlyCallManager

    def __init__(self, options: DoNotUseDirectlyCallManager):
        self.__options = options

    def AnthropicCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AnthropicCodeManager", args={
            "tool_results": tool_results,"system_info": system_info,
        }, mode="stream")
        return result
    def AnthropicConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AnthropicConnectionMatching", args={
            "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
        }, mode="stream")
        return result
    def AnthropicConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AnthropicConnectionSplitting", args={
            "memory_context": memory_context,
        }, mode="stream")
        return result
    def AnthropicImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AnthropicImplementationDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="stream")
        return result
    def AnthropicImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AnthropicImportDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="stream")
        return result
    def AnthropicPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AnthropicPackageDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="stream")
        return result
    def AnthropicRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AnthropicRoadmapAgent", args={
            "params": params,
        }, mode="stream")
        return result
    def AnthropicTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AnthropicTaskFilter", args={
            "task_list": task_list,
        }, mode="stream")
        return result
    def AnthropicTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AnthropicTechnologyCorrection", args={
            "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
        }, mode="stream")
        return result
    def AwsCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AwsCodeManager", args={
            "tool_results": tool_results,"system_info": system_info,
        }, mode="stream")
        return result
    def AwsConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AwsConnectionMatching", args={
            "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
        }, mode="stream")
        return result
    def AwsConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AwsConnectionSplitting", args={
            "memory_context": memory_context,
        }, mode="stream")
        return result
    def AwsImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AwsImplementationDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="stream")
        return result
    def AwsImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AwsImportDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="stream")
        return result
    def AwsPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AwsPackageDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="stream")
        return result
    def AwsRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AwsRoadmapAgent", args={
            "params": params,
        }, mode="stream")
        return result
    def AwsTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AwsTaskFilter", args={
            "task_list": task_list,
        }, mode="stream")
        return result
    def AwsTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="AwsTechnologyCorrection", args={
            "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
        }, mode="stream")
        return result
    def ChatGPTCodeManager(self, tool_results: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="ChatGPTCodeManager", args={
            "tool_results": tool_results,"system_info": system_info,
        }, mode="stream")
        return result
    def ChatGPTConnectionMatching(self, incoming_connections: str,outgoing_connections: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="ChatGPTConnectionMatching", args={
            "incoming_connections": incoming_connections,"outgoing_connections": outgoing_connections,
        }, mode="stream")
        return result
    def ChatGPTConnectionSplitting(self, memory_context: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="ChatGPTConnectionSplitting", args={
            "memory_context": memory_context,
        }, mode="stream")
        return result
    def ChatGPTImplementationDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="ChatGPTImplementationDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="stream")
        return result
    def ChatGPTImportDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="ChatGPTImportDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="stream")
        return result
    def ChatGPTPackageDiscovery(self, analysis_query: str,memory_context: str,system_info: types.SystemInfo_CrossIndexing,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="ChatGPTPackageDiscovery", args={
            "analysis_query": analysis_query,"memory_context": memory_context,"system_info": system_info,
        }, mode="stream")
        return result
    def ChatGPTRoadmapAgent(self, params: types.RoadmapAgentParams,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="ChatGPTRoadmapAgent", args={
            "params": params,
        }, mode="stream")
        return result
    def ChatGPTTaskFilter(self, task_list: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="ChatGPTTaskFilter", args={
            "task_list": task_list,
        }, mode="stream")
        return result
    def ChatGPTTechnologyCorrection(self, unmatched_names: str,acceptable_enums: str,
        baml_options: BamlCallOptions = {},
    ) -> baml_py.baml_py.HTTPRequest:
        result = self.__options.merge_options(baml_options).create_http_request_sync(function_name="ChatGPTTechnologyCorrection", args={
            "unmatched_names": unmatched_names,"acceptable_enums": acceptable_enums,
        }, mode="stream")
        return result


b = BamlSyncClient(DoNotUseDirectlyCallManager({}))

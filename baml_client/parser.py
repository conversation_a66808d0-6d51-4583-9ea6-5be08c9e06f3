# ----------------------------------------------------------------------------
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml
#
# ----------------------------------------------------------------------------

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code using: baml-cli generate
# baml-cli is available with the baml package.

import typing
import typing_extensions

from . import stream_types, types
from .runtime import DoNotUseDirectlyCallManager, BamlCallOptions

class LlmResponseParser:
    __options: DoNotUseDirectlyCallManager

    def __init__(self, options: DoNotUseDirectlyCallManager):
        self.__options = options

    def AnthropicCodeManager(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.CodeManagerResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AnthropicCodeManager", llm_response=llm_response, mode="request")
        return typing.cast(types.CodeManagerResponse, result)

    def AnthropicConnectionMatching(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.ConnectionMatchingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AnthropicConnectionMatching", llm_response=llm_response, mode="request")
        return typing.cast(types.ConnectionMatchingResponse, result)

    def AnthropicConnectionSplitting(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.ConnectionSplittingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AnthropicConnectionSplitting", llm_response=llm_response, mode="request")
        return typing.cast(types.ConnectionSplittingResponse, result)

    def AnthropicImplementationDiscovery(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AnthropicImplementationDiscovery", llm_response=llm_response, mode="request")
        return typing.cast(types.CrossIndexingResponse, result)

    def AnthropicImportDiscovery(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AnthropicImportDiscovery", llm_response=llm_response, mode="request")
        return typing.cast(types.CrossIndexingResponse, result)

    def AnthropicPackageDiscovery(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AnthropicPackageDiscovery", llm_response=llm_response, mode="request")
        return typing.cast(types.CrossIndexingResponse, result)

    def AnthropicRoadmapAgent(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.RoadmapResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AnthropicRoadmapAgent", llm_response=llm_response, mode="request")
        return typing.cast(types.RoadmapResponse, result)

    def AnthropicTaskFilter(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.TaskFilterResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AnthropicTaskFilter", llm_response=llm_response, mode="request")
        return typing.cast(types.TaskFilterResponse, result)

    def AnthropicTechnologyCorrection(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.TechnologyCorrectionResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AnthropicTechnologyCorrection", llm_response=llm_response, mode="request")
        return typing.cast(types.TechnologyCorrectionResponse, result)

    def AwsCodeManager(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.CodeManagerResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AwsCodeManager", llm_response=llm_response, mode="request")
        return typing.cast(types.CodeManagerResponse, result)

    def AwsConnectionMatching(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.ConnectionMatchingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AwsConnectionMatching", llm_response=llm_response, mode="request")
        return typing.cast(types.ConnectionMatchingResponse, result)

    def AwsConnectionSplitting(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.ConnectionSplittingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AwsConnectionSplitting", llm_response=llm_response, mode="request")
        return typing.cast(types.ConnectionSplittingResponse, result)

    def AwsImplementationDiscovery(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AwsImplementationDiscovery", llm_response=llm_response, mode="request")
        return typing.cast(types.CrossIndexingResponse, result)

    def AwsImportDiscovery(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AwsImportDiscovery", llm_response=llm_response, mode="request")
        return typing.cast(types.CrossIndexingResponse, result)

    def AwsPackageDiscovery(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AwsPackageDiscovery", llm_response=llm_response, mode="request")
        return typing.cast(types.CrossIndexingResponse, result)

    def AwsRoadmapAgent(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.RoadmapResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AwsRoadmapAgent", llm_response=llm_response, mode="request")
        return typing.cast(types.RoadmapResponse, result)

    def AwsTaskFilter(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.TaskFilterResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AwsTaskFilter", llm_response=llm_response, mode="request")
        return typing.cast(types.TaskFilterResponse, result)

    def AwsTechnologyCorrection(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.TechnologyCorrectionResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AwsTechnologyCorrection", llm_response=llm_response, mode="request")
        return typing.cast(types.TechnologyCorrectionResponse, result)

    def ChatGPTCodeManager(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.CodeManagerResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="ChatGPTCodeManager", llm_response=llm_response, mode="request")
        return typing.cast(types.CodeManagerResponse, result)

    def ChatGPTConnectionMatching(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.ConnectionMatchingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="ChatGPTConnectionMatching", llm_response=llm_response, mode="request")
        return typing.cast(types.ConnectionMatchingResponse, result)

    def ChatGPTConnectionSplitting(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.ConnectionSplittingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="ChatGPTConnectionSplitting", llm_response=llm_response, mode="request")
        return typing.cast(types.ConnectionSplittingResponse, result)

    def ChatGPTImplementationDiscovery(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="ChatGPTImplementationDiscovery", llm_response=llm_response, mode="request")
        return typing.cast(types.CrossIndexingResponse, result)

    def ChatGPTImportDiscovery(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="ChatGPTImportDiscovery", llm_response=llm_response, mode="request")
        return typing.cast(types.CrossIndexingResponse, result)

    def ChatGPTPackageDiscovery(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.CrossIndexingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="ChatGPTPackageDiscovery", llm_response=llm_response, mode="request")
        return typing.cast(types.CrossIndexingResponse, result)

    def ChatGPTRoadmapAgent(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.RoadmapResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="ChatGPTRoadmapAgent", llm_response=llm_response, mode="request")
        return typing.cast(types.RoadmapResponse, result)

    def ChatGPTTaskFilter(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.TaskFilterResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="ChatGPTTaskFilter", llm_response=llm_response, mode="request")
        return typing.cast(types.TaskFilterResponse, result)

    def ChatGPTTechnologyCorrection(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> types.TechnologyCorrectionResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="ChatGPTTechnologyCorrection", llm_response=llm_response, mode="request")
        return typing.cast(types.TechnologyCorrectionResponse, result)



class LlmStreamParser:
    __options: DoNotUseDirectlyCallManager

    def __init__(self, options: DoNotUseDirectlyCallManager):
        self.__options = options

    def AnthropicCodeManager(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.CodeManagerResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AnthropicCodeManager", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.CodeManagerResponse, result)

    def AnthropicConnectionMatching(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.ConnectionMatchingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AnthropicConnectionMatching", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.ConnectionMatchingResponse, result)

    def AnthropicConnectionSplitting(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.ConnectionSplittingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AnthropicConnectionSplitting", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.ConnectionSplittingResponse, result)

    def AnthropicImplementationDiscovery(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.CrossIndexingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AnthropicImplementationDiscovery", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.CrossIndexingResponse, result)

    def AnthropicImportDiscovery(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.CrossIndexingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AnthropicImportDiscovery", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.CrossIndexingResponse, result)

    def AnthropicPackageDiscovery(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.CrossIndexingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AnthropicPackageDiscovery", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.CrossIndexingResponse, result)

    def AnthropicRoadmapAgent(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.RoadmapResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AnthropicRoadmapAgent", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.RoadmapResponse, result)

    def AnthropicTaskFilter(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.TaskFilterResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AnthropicTaskFilter", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.TaskFilterResponse, result)

    def AnthropicTechnologyCorrection(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.TechnologyCorrectionResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AnthropicTechnologyCorrection", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.TechnologyCorrectionResponse, result)

    def AwsCodeManager(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.CodeManagerResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AwsCodeManager", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.CodeManagerResponse, result)

    def AwsConnectionMatching(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.ConnectionMatchingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AwsConnectionMatching", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.ConnectionMatchingResponse, result)

    def AwsConnectionSplitting(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.ConnectionSplittingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AwsConnectionSplitting", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.ConnectionSplittingResponse, result)

    def AwsImplementationDiscovery(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.CrossIndexingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AwsImplementationDiscovery", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.CrossIndexingResponse, result)

    def AwsImportDiscovery(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.CrossIndexingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AwsImportDiscovery", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.CrossIndexingResponse, result)

    def AwsPackageDiscovery(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.CrossIndexingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AwsPackageDiscovery", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.CrossIndexingResponse, result)

    def AwsRoadmapAgent(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.RoadmapResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AwsRoadmapAgent", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.RoadmapResponse, result)

    def AwsTaskFilter(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.TaskFilterResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AwsTaskFilter", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.TaskFilterResponse, result)

    def AwsTechnologyCorrection(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.TechnologyCorrectionResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="AwsTechnologyCorrection", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.TechnologyCorrectionResponse, result)

    def ChatGPTCodeManager(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.CodeManagerResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="ChatGPTCodeManager", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.CodeManagerResponse, result)

    def ChatGPTConnectionMatching(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.ConnectionMatchingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="ChatGPTConnectionMatching", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.ConnectionMatchingResponse, result)

    def ChatGPTConnectionSplitting(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.ConnectionSplittingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="ChatGPTConnectionSplitting", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.ConnectionSplittingResponse, result)

    def ChatGPTImplementationDiscovery(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.CrossIndexingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="ChatGPTImplementationDiscovery", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.CrossIndexingResponse, result)

    def ChatGPTImportDiscovery(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.CrossIndexingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="ChatGPTImportDiscovery", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.CrossIndexingResponse, result)

    def ChatGPTPackageDiscovery(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.CrossIndexingResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="ChatGPTPackageDiscovery", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.CrossIndexingResponse, result)

    def ChatGPTRoadmapAgent(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.RoadmapResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="ChatGPTRoadmapAgent", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.RoadmapResponse, result)

    def ChatGPTTaskFilter(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.TaskFilterResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="ChatGPTTaskFilter", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.TaskFilterResponse, result)

    def ChatGPTTechnologyCorrection(
        self, llm_response: str, baml_options: BamlCallOptions = {},
    ) -> stream_types.TechnologyCorrectionResponse:
        result = self.__options.merge_options(baml_options).parse_response(function_name="ChatGPTTechnologyCorrection", llm_response=llm_response, mode="stream")
        return typing.cast(stream_types.TechnologyCorrectionResponse, result)

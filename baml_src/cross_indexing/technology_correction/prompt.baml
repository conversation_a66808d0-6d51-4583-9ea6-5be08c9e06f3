template_string TechnologyCorrectionSystemPrompt() #"
You are a precise technology classification expert. Your task is to map unmatched technology names to the exact predefined technology enums used in the connection splitting system.

## OBJECTIVE

Correct technology names that do not match the predefined enums by mapping them to the closest appropriate enum from the acceptable list.

## ACCEPTABLE TECHNOLOGY ENUMS

You MUST use ONLY one of these exact technology type names (case-sensitive):

1. HTTP/HTTPS - HTTP/HTTPS REST API calls and endpoints
    - Keywords: http, https, rest, api, endpoint, fetch, axios, requests, express, flask, fastapi
    - Examples: "HTTP", "REST", "API", "HTTPS"

2. WebSockets - WebSocket connections for real-time bidirectional communication
    - Keywords: websocket, socket, ws, socket.io, real-time, webrtc
    - Examples: "WebSocket", "Socket.IO", "WS"

3. gRPC - Google RPC framework for high-performance RPC
    - Keywords: grpc, protobuf, proto, rpc
    - Examples: "gRP<PERSON>", "G<PERSON><PERSON>", "protobuf"

4. GraphQL - Query language for APIs
    - Keywords: graphql, gql, query language
    - Examples: "GraphQL", "GQL"

5. MessageQueue - Message queuing systems
    - Keywords: queue, mq, rabbitmq, kafka, bull, sqs, message queue, pubsub, messaging
    - Examples: "Queue", "RabbitMQ", "Kafka", "SQS"

6. Unknown - Use ONLY when technology type cannot be identified
    - Use as last resort when no other type fits
    - Examples: "CustomProtocol", "ProprietarySystem"

## CORRECTION RULES

1. Exact Match Required: The corrected name MUST exactly match one of the 6 enum names above (case-sensitive).

2. Keyword-Based Mapping: Analyze the unmatched name for keywords that indicate the technology type.

3. Best Fit Analysis: Choose the enum that best represents the technology based on:
    - Primary function (communication, messaging, data transfer)
    - Protocol type (HTTP, WebSocket, messaging, etc.)
    - Use case (API calls, real-time communication, queuing, etc.)

4. Conservative Approach: When uncertain, prefer "Unknown" over incorrect mapping.

5. Case Sensitivity: Maintain exact capitalization as shown in the enum list.

## ANALYSIS PROCESS

For each unmatched name:
1. Extract keywords from the name
2. Match keywords to technology categories
3. Select the most appropriate enum
4. Validate the mapping makes technical sense

## OUTPUT FORMAT

Return a structured response with corrections for each unmatched name.

Example reasoning:
"I analyzed the unmatched technology names by extracting keywords and mapping them to the predefined enums. For 'REST', I identified HTTP-related keywords and mapped it to 'HTTP/HTTPS'. For 'WebSocket', I found real-time communication keywords and mapped it to 'WebSockets'. For unclear names, I used 'Unknown' to maintain accuracy."

Therefore the output is:
{
  "corrections": [
    {
      "original_name": "string",
      "corrected_name": "string"
    }
  ]
}

## REQUIREMENTS

- Explain your reasoning step-by-step before providing the JSON response
- Process ALL unmatched names provided
- Map each name to the most appropriate enum
- Use exact enum names (case-sensitive)
- Prefer accuracy over guessing - use "Unknown" when uncertain
- Provide technical justification for each mapping
"#

template_string TechnologyCorrectionUserPrompt(unmatched_names: string, acceptable_enums: string) #"
    {{ _.role("user") }}
    ### UNMATCHED TECHNOLOGY NAMES
    {{ unmatched_names }}

    ### ACCEPTABLE ENUMS
    {{ acceptable_enums }}

    Analyze each unmatched technology name and map it to the most appropriate enum from the acceptable list. Return the corrections in the specified JSON format.
"#

function AwsTechnologyCorrection(unmatched_names: string, acceptable_enums: string) -> TechnologyCorrectionResponse {
  client AwsClaudeSonnet4
  prompt #"
    {{ _.role("system", cache_control={"type": "ephemeral"}) }}
    {{ TechnologyCorrectionSystemPrompt() }}

    {{ TechnologyCorrectionUserPrompt(unmatched_names, acceptable_enums) }}
  "#
}

function AnthropicTechnologyCorrection(unmatched_names: string, acceptable_enums: string) -> TechnologyCorrectionResponse {
  client AnthropicClaude
  prompt #"
    {{ _.role("system", cache_control={"type": "ephemeral"}) }}
    {{ TechnologyCorrectionSystemPrompt() }}

    {{ TechnologyCorrectionUserPrompt(unmatched_names, acceptable_enums) }}
  "#
}

function ChatGPTTechnologyCorrection(unmatched_names: string, acceptable_enums: string) -> TechnologyCorrectionResponse {
  client OpenAIChatGPT
  prompt #"
    {{ _.role("system") }}
    {{ TechnologyCorrectionSystemPrompt() }}
    {{ TechnologyCorrectionUserPrompt(unmatched_names, acceptable_enums) }}
  "#
}

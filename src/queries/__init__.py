# Import agent queries (used by AI agent) - ONLY 6 EXPOSED QUERIES FOR AGENT
from .agent_queries import (
    GET_CHILD_BLOCKS,
    GET_DEPENDENCY_CHAIN,
    GET_FILE_BLOCK_SUMMARY,
    GET_FILE_BY_ID,
    GET_PARENT_BLOCK,
)

# Import graph queries (used by cross-indexing service)
from .graph_queries import (
    GET_CONNECTIONS_BY_IDS,
    GET_EXISTING_INCOMING_CONNECTIONS,
    GET_EXISTING_OUTGOING_CONNECTIONS,
    INSERT_CONNECTION_MAPPING,
    INSERT_INCOMING_CONNECTION,
    INSERT_OUTGOING_CONNECTION,
    UPDATE_PROJECT_DESCRIPTION,
)

__all__ = [
    "GET_FILE_BY_ID",
    "GET_FILE_BLOCK_SUMMARY",
    "GET_CHILD_BLOCKS",
    "GET_PARENT_BLOCK",
    "GET_DEPENDENCY_CHAIN",
    # G<PERSON><PERSON><PERSON> QUERIES FOR CROSS-INDEXING
    "GET_EXISTING_INCOMING_CONNECTIONS",
    "GET_EXISTING_OUTGOING_CONNECTIONS",
    "GET_CONNECTIONS_BY_IDS",
    "INSERT_INCOMING_CONNECTION",
    "INSERT_OUTGOING_CONNECTION",
    "INSERT_CONNECTION_MAPPING",
    "UPDATE_PROJECT_DESCRIPTION",
]
